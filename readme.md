## Project stacks

- Nodejs, Fastify, Postgresql
- Typescript
- Expo Web, React Native & React JS

## Run Backend Dev

Run DB: (need `docker`)
- `cd backend`
- `yarn run-local-db`

Run Backend:
- Check `backend/.nvmrc` for node version
- `cd backend`
- `yarn`
- `cp .env_clone_me .env`
- `yarn dev`

## API Client

When editing api codes, if `process.env.GENERATE_API_CLIENT` set to `1` it will automatically generate api client code at `./api-client`. We can later sync to cms using command `yarn sync-api-local` in `cms` folder.

