/// <reference types="cypress" />

describe('Logging into Pathfinder as Learner', () => {
  beforeEach(() => {
    cy.visit('http://localhost:19007/demo-client/us/pathfinder0');
  })

  it('Show contact form at first, Then I can see elements and able to filter', () => {
    cy.get('[data-element="contact-form"]').should('be.visible')
    cy.get('[data-element="input-firstName"]').eq(0).type('John');
    cy.get('[data-element="input-lastName"]').eq(0).type('Doe');
    cy.get('[data-element="input-email"]').eq(0).type('<EMAIL>');
    cy.wait(500);

    cy.get('[data-element="submit-contact-form"]').first().click();
    cy.get('[data-element="element-item"]').should('have.length', 4);
    cy.get('[data-element="element-item"]').eq(0).should('contain.text', 'Author');
    cy.get('[data-element="element-item"]').eq(1).should('contain.text', 'Matter manager');
    cy.wait(500);

    cy.get('[data-element="filter-button"]').first().click();
    cy.get('[data-element="filter-key-0"]').first().click();
    cy.wait(500);

    cy.get('[data-element="element-item"]').should('have.length.lessThan', 4);
    cy.wait(500);
    cy.get('[data-element="filter-key-1"]').first().click();
    cy.get('[data-element="element-item"]').should('have.length.lessThan', 4);
    cy.wait(500);
  });

  it('clicking one element will show list of its statements', () => {
    cy.get('[data-element="contact-form"]').should('be.visible')
    cy.get('[data-element="input-firstName"]').eq(0).type('John');
    cy.get('[data-element="input-lastName"]').eq(0).type('Doe');
    cy.get('[data-element="input-email"]').eq(0).type('<EMAIL>');
    cy.wait(500);

    cy.get('[data-element="submit-contact-form"]').first().click();
    cy.get('[data-element="element-item"]').should('have.length', 4);
    cy.get('[data-element="element-item"]').eq(0).should('contain.text', 'Author');
    cy.get('[data-element="element-item"]').eq(1).should('contain.text', 'Matter manager');
    cy.wait(500);

    cy.get('[data-element="element-item"]').eq(0).click();
    cy.get('[data-element="statement-list"]').should('be.visible');
    cy.wait(1000);

    cy.get('[id="liker-slider"]').eq(0).click('left');
    cy.wait(500);
    cy.get('[id="liker-slider"]').eq(1).click('right');
    cy.wait(500);

    cy.get('[data-element="submit-answers"]').click();
    cy.get('[id="table-row"]').should('have.length.greaterThan', 0);
    cy.wait(2000);

    cy.reload(true);
    cy.wait(500);
    cy.get('[data-element="submit-contact-form"]').last().click();
    cy.get('[data-element="element-item"]').should('have.length', 4);
    cy.wait(1000);
    cy.get('[id="menu-learning-plan"]').last().click();
    cy.get('[id="table-row"]').should('have.length.greaterThan', 0);
    cy.wait(2000);
  });

})
