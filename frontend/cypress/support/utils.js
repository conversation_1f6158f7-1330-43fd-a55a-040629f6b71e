export const typingToPathfinderInput = async (idx, colIdx, text) => {
  cy.get('[data-element="pathfinder-layout"]').eq(idx).find('[data-element="row-input"]')
    .eq(colIdx).type(text, {
      force: true,
      waitForAnimations: false,
    });
}

export const checkPathfinderInput = async (idx, colIdx, text) => {
  cy.get('[data-element="pathfinder-layout"]').eq(idx).find('[data-element="row-input"]')
    .eq(colIdx).should('have.value', text);
}
