{"name": "cms", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start --web --port 19007", "web-build": "npx expo export:web", "build": "rm -rf build && yarn web-build && mv web-build build", "sync-api-local": "rm -rf ./src/store/api-client && cp -r ../api-client ./src/store", "nginx-deploy": "yarn build && rm -rf /var/www/frontend && cp -r build /var/www/frontend", "test": "npx cypress open", "postinstall": "patch-package", "postuninstall": "patch-package"}, "dependencies": {"@expo/vector-icons": "^13.0.0", "@expo/webpack-config": "^0.17.0", "@react-native-community/masked-view": "0.1.10", "@react-native-community/slider": "^4.4.2", "@react-navigation/native": "^5.9.4", "@react-navigation/stack": "^5.14.5", "@react-navigation/tabs": "^0.0.0-alpha.12", "@stripe/react-stripe-js": "^1.16.1", "@stripe/stripe-js": "^1.46.0", "antd": "^5.13.3", "expo": "~46.0.8", "expo-status-bar": "~1.4.0", "file-saver": "^2.0.5", "lighten-darken-color": "^1.0.0", "lodash": "^4.17.21", "moment": "^2.29.4", "quickly-react": "0.1.2", "react": "18.0.0", "react-dom": "18.0.0", "react-native": "0.69.4", "react-native-animatable": "^1.3.3", "react-native-gesture-handler": "~2.5.0", "react-native-reanimated": "~2.9.1", "react-native-safe-area-context": "4.3.1", "react-native-screens": "~3.15.0", "react-native-svg": "12.3.0", "react-native-web": "~0.18.7", "react-select": "^5.4.0", "react-tippy": "^1.4.0", "route-parser": "^0.0.5", "scheduler": "^0.23.0", "uuidv4": "^6.2.13", "zustand": "^4.1.0"}, "devDependencies": {"@babel/core": "^7.12.9", "@types/lodash": "^4.14.195", "@types/node": "^18.7.6", "@types/react": "^18.0.17", "@types/react-native": "^0.69.5", "babel-plugin-module-resolver": "^4.1.0", "cypress": "^12.15.0", "cypress-fail-fast": "^7.0.1", "patch-package": "^7.0.0", "typescript": "^4.7.4"}, "private": true}