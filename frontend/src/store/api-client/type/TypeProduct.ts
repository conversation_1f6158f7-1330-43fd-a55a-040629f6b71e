

export type TProduct = {
  id: string,
  name: string,
  description?: string,
  image: string,
  bluePrintImage: string,
  galleries: Array<string>,
  category?: string,
  unit?: 'mm' | 'inch',
  physicalWidth: number,
  physicalHeight: number,
  printAreas: Array<{
    width: number,
    height: number,
    top: number,
    left: number,
  }>,
  data?: any,
  availableForResellerIds: {
    [resellerId: string]: boolean,
  },
  tags?: string,
  createdByUserId: string,
  createdByUserType: string,
  variations?: Array<{
    variant: string,
    prices: Array<{
      amount: string | number,
      price: string | number,
    }>,
  }>,
  originalImages?: {
    bluePrint: string,
    galleries: Array<string>,
    image: string,
  },
}

export type TDesign = {
  id: string,
  productId: string,
  name: string,
  image: string,
  unit?: 'mm' | 'inch',
  width: number,
  height: number,
  printAreas: TProduct['printAreas'],
  data?: any,
  availableForResellerIds: {
    [resellerId: string]: boolean,
  },
  isCustomizable: boolean,
  createdByUserId: string,
  createdByUserType: string,
  products?: Array<{
    url: string,
    storeId: string,
    productId: string,
    productAdminUrl: string,
  }>
}

export type TProductInstance = {
  id: string,
  designId: string,
  productId: string,
  name: string,
  image: string,
  isCustomizable: boolean,
  createdByUserId: string,
  createdByUserType: string,
  price: string,
  skuNumber: string,
}