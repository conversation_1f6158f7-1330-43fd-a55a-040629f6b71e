import { TProduct } from "./TypeProduct";

export type TPrintJob = {
  id: string;
  clientId: string;
  productId: string;
  designId: string;
  productName: string;

  entityId?: string;

  previewUrl: string;
  artworkUrls: Array<string>;

  quantity: number;

  isPrinted: boolean;
  isPDFDownloaded: boolean;
  isRePrinted: boolean;

  data?: {
    product: {
      physicalWidth: TProduct['physicalWidth'],
      physicalHeight: TProduct['physicalHeight'],
      printAreas: TProduct['printAreas'],
    },
    [other: string]: any,
  };
  readyForPrint?: boolean;

  // db fields
  createdAt?: string;
  updatedAt?: string;
}