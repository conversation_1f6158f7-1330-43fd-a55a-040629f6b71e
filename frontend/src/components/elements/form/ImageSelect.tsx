import { UploadFile, TouchField, Image, Col } from 'components';
import { COLOR } from 'const';
import React, { useRef, useState } from 'react';
import { ActivityIndicator, ImageResizeMode, ImageStyle, StyleProp } from 'react-native';

export interface IImageSelectProps {
  value: string;
  resizeMode?: ImageResizeMode;
  imageStyle?: StyleProp<ImageStyle>;
  onChange?: (url: string) => void;
}

const ImageSelect = (props: IImageSelectProps) => {
  const { value, imageStyle, resizeMode, onChange } = props;
  const uploadRef = useRef<{ showDialog: Function }>(null);
  const [loading, setLoading] = useState(false);

  const handleButtonClick = () => {
    setLoading(true);
    uploadRef.current.showDialog();
  }

  const onImageUploaded = (urls) => {
    if (urls.length === 0) return setLoading(false);
    onChange(urls[0]);
    setLoading(false);
  }

  return (
    <TouchField dataSet={{ element: 'image-picker' }} onPress={handleButtonClick}>
      <Image
        source={{ uri: value }}
        style={[{ width: '100%', height: 70 }, imageStyle]}
        borderRadius={10}
        resizeMode={resizeMode || 'contain'}
      />
      <UploadFile
        ref={uploadRef}
        onUploaded={onImageUploaded}
      />
      {loading &&
        <Col absoluteFill middle backgroundColor={'rgba(0,0,0,0.05)'}>
          <ActivityIndicator color={COLOR.MAIN} testID='image-uploading' />
        </Col>
      }
    </TouchField>
  )
}

export default ImageSelect;
