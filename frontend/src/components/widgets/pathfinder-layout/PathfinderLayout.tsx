import React, { useState, useRef, useEffect, useMemo } from 'react';
import { Col, IColProps, Row, Text, TouchField, Grid, RowTableLoading, CheckBox, ImageSelect } from 'components';
import { ScrollView, TextInput, TextInputProps } from 'react-native';
import { COLOR } from 'const';
import { TElement, TPathfinder, TStatement } from 'type';
import { IRowProps } from 'components/base/Row';
import { FontAwesome5 } from '@expo/vector-icons';
import Store from 'store';
import { usePathfinder } from 'store/Pathfinder.Store';
import { useElements } from 'store/Element.Store';
import { useStatements } from 'store/Statement.Store';
import { SelectTagInput } from 'components/elements/form/Select';
import { isArray } from 'lodash';

interface Props extends IColProps {
  pathfinderId?: string
}

const RowTable = ({ ...props }: IRowProps) => <Row borderBottomWidth={1} borderBottomColor={COLOR.GREY_LIGHT} {...props} />;
const RowInput = ({ ...props }: TextInputProps) => {
  const [height, setHeight] = useState(32)
  return (
    <TextInput
      multiline
      // @ts-ignore
      style={{ outline: 'none', padding: 5, height, maxHeight: 200 }}
      onContentSizeChange={e => setHeight(e.nativeEvent.contentSize.height)}
      dataSet={{ element: 'row-input' }}
      {...props}
    />
  )
};
const RowInputWithHint = ({ hint, ...props }: TextInputProps & { hint: string }) => {
  const [showHint, setShowHint] = useState(false);
  return (
    <Col>
      {!!showHint && (
        <Row mt0 stretch ml0>
          <Col width={2} backgroundColor={COLOR.MAIN} />
          <Col flex1 ml0>
            <Text caption>{hint}</Text>
          </Col>
        </Row>
      )}
      <Row alignItems={'flex-start'}>
        <Col flex1>
          <RowInput {...props} />
        </Col>
        {!!hint && (
          <TouchField width={24} height={24} borderRadius={12} middle mt0 ml0 onPress={() => setShowHint(v => !v)}>
            <FontAwesome5 name="info" size={14} color={COLOR.MAIN} />
          </TouchField>
        )}
      </Row>
    </Col>
  )
};
const LineVertical = () => <Col height100p width={1} backgroundColor={COLOR.GREY_LIGHT} />;

const PathdinderLayout = ({ pathfinderId, ...props }: Props) => {
  const [isHover, setIsHover] = useState(false);
  const pathfinder: TPathfinder = usePathfinder(pathfinderId);
  const elements: TElement[] = useElements(pathfinderId);
  const statements: TStatement[] = useStatements(pathfinderId);
  const [tab, setTab] = useState('info');
  const { editPathfinder, autoUpdate: autoUpdatePathfinders } = Store.usePathfinderStore(state => ({
    autoUpdate: state.autoUpdate,
    editPathfinder: state.editPathfinder,
  }));
  const { loading: loadingElements, getElements, addEmptyElement, editElement, autoUpdate: autoUpdateElements } = Store.useElementStore();
  const { loading: loadingStatements, getStatements, addEmptyStatement, editStatement, autoUpdate: autoUpdateStatements } = Store.useStatementStore();

  useEffect(() => {
    if (tab === 'elements' && loadingElements?.[pathfinderId] === undefined) {
      getElements(pathfinderId);
    }
    if (tab === 'work-statements' && loadingStatements?.[pathfinderId] === undefined) {
      getStatements(pathfinderId);
    }
  }, [tab, pathfinder]);

  const allFilterKeys = useMemo(() => {
    const result = [];
    if (!isArray(result)) return result;
    for (let elem of elements) {
      if (elem?.filterKeys) {
        elem.filterKeys.split(',').forEach(key => {
          if (!result.includes(key)) result.push(key)
        })
      }
    }
    return result.map(i => ({ label: i, value: i }));
  }, [elements])

  const TABS = [
    { label: 'Info', key: 'info' },
    { label: 'Email Settings', key: 'email-settings' },
    { label: 'Elements', key: 'elements' },
    { label: 'Work Statements', key: 'work-statements' },
  ];

  const onChangeTab = (key) => {
    if (tab === 'info') {
      autoUpdatePathfinders()
    }
    if (tab === 'elements') {
      autoUpdateElements()
    }
    if (tab === 'work-statements') {
      autoUpdateStatements()
    }
    setTab(key)
  }

  const infoFields = [
    {
      label: 'Pathfinder Name',
      value: pathfinder?.name,
      fieldKey: 'name',
    },
    {
      label: 'Welcome Instruction',
      value: pathfinder?.welcomeInstruction,
      fieldKey: 'welcomeInstruction',
      hint: 'Available Filtered Variables: {{.PathfinderName}}',
    },
    {
      label: 'Additional Instruction',
      value: pathfinder?.additionalInstruction,
      fieldKey: 'additionalInstruction',
      hint: '',
    },
    {
      label: 'Completion Dialog Instructions',
      value: pathfinder?.completionInstruction,
      fieldKey: 'completionInstruction',
      hint: 'Available Filtered Variables: {{.FirstName}}, {{.PathfinderName}}',
    },
    {
      label: 'Screen Element Title',
      value: pathfinder?.elementsTitle,
      fieldKey: 'elementsTitle',
      hint: '',
    },
    {
      label: 'Likert Scale Left',
      value: pathfinder?.likertScaleTitle1,
      fieldKey: 'likertScaleTitle1',
    },
    {
      label: 'Likert Scale Middle',
      value: pathfinder?.likertScaleTitle2,
      fieldKey: 'likertScaleTitle2',
    },
    {
      label: 'Likert Scale Right',
      value: pathfinder?.likertScaleTitle3,
      fieldKey: 'likertScaleTitle3',
    },
  ];

  const emailSettingsFields = [
    {
      label: 'Send To User',
      value: pathfinder?.sendEmail,
      fieldKey: 'sendEmail',
    },
    {
      label: 'Additional recipients',
      value: pathfinder?.additionalRecipients,
      fieldKey: 'additionalRecipients',
      hint: 'Enter email address of the users (separated by comma) you wish to view this email as well as the users that complete the pathfinder.',
    },
    {
      label: 'Subject Line',
      value: pathfinder?.emailSubject,
      fieldKey: 'emailSubject',
      hint: '',
    },
    {
      label: 'Welcome & Body Content',
      value: pathfinder?.emailContent,
      fieldKey: 'emailContent',
      hint: 'Available Filtered Variables: {{.FirstName}}, {{.PathfinderName}}',
    },
    {
      label: 'Body footer',
      value: pathfinder?.emailFooter,
      fieldKey: 'emailFooter',
      hint: '',
    },
  ];

  const addEmptyPathfinderEntity = () => {
    if (tab === 'elements') {
      addEmptyElement(pathfinderId);
    }
    if (tab === 'work-statements') {
      addEmptyStatement(pathfinderId);
    }
  }

  return (
    <Col
      onMouseEnter={() => setIsHover(true)}
      onMouseLeave={() => setIsHover(false)}
      backgroundColor={isHover ? COLOR.GREY_LIGHT : 'white'}
      minHeight={150}
      borderRadius={10}
      borderWidth={1}
      borderColor={'rgba(0,0,0,0.08)'}
      shadow1={isHover}
      dataSet={{ element: 'pathfinder-layout' }}
      {...props}
    >
      <Grid xs='100%' md=':1' stretch height100p>
        <Col m0 width={200} overflow={'hidden'}>
          <ImageSelect
            value={pathfinder?.logo}
            imageStyle={{ width: '100%', height: 140 }}
            onChange={(url) => editPathfinder(pathfinderId, 'logo', url)}
          />
        </Col>
        <Col flex1 m0 p0 bgWhite round1>
          <Row justifyContent={'space-between'}>
            <Row>
              {TABS.map((v, vI) => {
                const isActive = tab === v.key;
                return (
                  <TouchField
                    key={'tab-' + vI} dataSet={{ element: 'pathfinder-tab' }}
                    borderRadius={0} p0 mr0 borderBottomWidth={isActive ? 2 : 0} borderBottomColor={COLOR.MAIN} onPress={() => onChangeTab(v.key)}>
                    <Text caption color={isActive ? COLOR.MAIN : undefined}>{v.label}</Text>
                  </TouchField>
                );
              })}
            </Row>
            {['elements', 'work-statements'].includes(tab) &&
              <TouchField
                width={30} height={30}
                borderRadius={15} middle
                onPress={addEmptyPathfinderEntity}
                dataSet={{ element: 'add-pathfinder-entity' }}
              >
                <FontAwesome5 name='plus-circle' size={20} color={COLOR.MAIN} />
              </TouchField>
            }
          </Row>
          <Col mt1>
            {tab === 'info' && (
              <>
                {infoFields.map((v, vI) => {
                  return (
                    <RowTable key={'info-row-' + vI}>
                      <Col flex1>
                        <Text caption bold>{v.label}</Text>
                      </Col>
                      <LineVertical />
                      <Col flex1 ml0>
                        <RowInputWithHint
                          value={v.value}
                          onChangeText={(text) => editPathfinder(pathfinderId, v.fieldKey, text)}
                          hint={v.hint}
                        />
                      </Col>
                    </RowTable>
                  )
                })}
              </>
            )}
            {tab === 'email-settings' && (
              <>
                {emailSettingsFields.map((v, vI) => {
                  if (v.fieldKey === 'sendEmail') {
                    return (
                      <RowTable key={'info-row-' + vI}>
                        <Col flex1>
                          <Text caption bold>{v.label}</Text>
                        </Col>
                        <LineVertical />
                        <Col flex1 ml0>
                          <CheckBox
                            value={v.value as boolean}
                            onChange={(val) => editPathfinder(pathfinderId, v.fieldKey, val)}
                          />
                        </Col>
                      </RowTable>
                    )
                  }
                  return (
                    <RowTable key={'info-row-' + vI}>
                      <Col flex1>
                        <Text caption bold>{v.label}</Text>
                      </Col>
                      <LineVertical />
                      <Col flex1 ml0>
                        <RowInputWithHint
                          value={v.value as string}
                          onChangeText={(text) => editPathfinder(pathfinderId, v.fieldKey, text)}
                          hint={v.hint}
                        />
                      </Col>
                    </RowTable>
                  )
                })}
              </>
            )}
            {tab === 'elements' && (
              <>
                <RowTable>
                  <Col flex1>
                    <Text caption bold m0>Name</Text>
                  </Col>
                  <LineVertical />
                  <Col flex1 >
                    <Text caption bold m0>Rollover Text</Text>
                  </Col>
                  <LineVertical />
                  <Col flex1>
                    <Text caption bold m0>Filters</Text>
                  </Col>
                  <LineVertical />
                  <Col flex1>
                    <Text caption bold m0>Image</Text>
                  </Col>
                </RowTable>
                {loadingElements?.[pathfinderId] ?
                  <RowTableLoading numOfCol={4} />
                  :
                  elements?.map(i => (
                    <RowTable key={`elm${pathfinderId}${i.id}`}>
                      <Col flex1>
                        <RowInput value={i.name} onChangeText={(text) => editElement(i.id, 'name', text)} />
                      </Col>
                      <LineVertical />
                      <Col flex1>
                        <RowInput value={i.rolloverText} onChangeText={(text) => editElement(i.id, 'rolloverText', text)} />
                      </Col>
                      <LineVertical />
                      <Col flex1>
                        <SelectTagInput
                          options={allFilterKeys}
                          value={i.filterKeys}
                          onChange={(opts) => editElement(i.id, 'filterKeys', opts.map(i => i.value).join(','))}
                          isMulti
                          dataSet={{ element: 'select-filter-keys' }}
                        />
                      </Col>
                      <LineVertical />
                      <Col flex1>
                        <Col m0 overflow={'hidden'}>
                          <ImageSelect
                            value={i.image}
                            imageStyle={{ width: '100%', height: 70 }}
                            onChange={(url) => editElement(i.id, 'image', url)}
                          />
                        </Col>
                      </Col>
                    </RowTable>
                  ))
                }
              </>
            )}
            {tab === 'work-statements' && (
              <>
                <RowTable>
                  <Col flex1>
                    <Text caption bold m0>Statement</Text>
                  </Col>
                  <LineVertical />
                  <Col flex1 >
                    <Text caption bold m0>Rollover Left</Text>
                  </Col>
                  <LineVertical />
                  <Col flex1>
                    <Text caption bold m0>Rollover Middle</Text>
                  </Col>
                  <LineVertical />
                  <Col flex1>
                    <Text caption bold m0>Rollover Right</Text>
                  </Col>
                </RowTable>
                {loadingStatements?.[pathfinderId] ?
                  <RowTableLoading numOfCol={4} />
                  :
                  statements?.map(i => (
                    <RowTable key={`stm${pathfinderId}${i.id}`}>
                      <Col flex1>
                        <RowInput value={i.statement} onChangeText={(text) => editStatement(i.id, 'statement', text)} />
                      </Col>
                      <LineVertical />
                      <Col flex1>
                        <RowInput value={i.rolloverLikert1} onChangeText={(text) => editStatement(i.id, 'rolloverLikert1', text)} />
                      </Col>
                      <LineVertical />
                      <Col flex1>
                        <RowInput value={i.rolloverLikert2} onChangeText={(text) => editStatement(i.id, 'rolloverLikert2', text)} />
                      </Col>
                      <LineVertical />
                      <Col flex1>
                        <RowInput value={i.rolloverLikert3} onChangeText={(text) => editStatement(i.id, 'rolloverLikert3', text)} />
                      </Col>
                    </RowTable>
                  ))
                }
              </>
            )}
          </Col>
        </Col>
      </Grid>
    </Col>
  )
};

export default React.memo(PathdinderLayout)
