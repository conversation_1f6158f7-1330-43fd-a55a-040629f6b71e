import * as React from "react"
import { SVGProps } from "react"

const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={25}
    height={27}
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
    viewBox="0 0 25 27"
    {...props}
  >
    <path fill="url(#a)" d="M0 0h25v27H0z" />
    <defs>
      <pattern
        id="a"
        patternContentUnits="objectBoundingBox"
        width={1}
        height={1}
      >
        <use xlinkHref="#b" transform="matrix(.0021 0 0 .00195 -.04 0)" />
      </pattern>
      <image
        id="b"
        width={512}
        height={512}
        xlinkHref="data:image/png;base64,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"
      />
    </defs>
  </svg>
)

export default SvgComponent
