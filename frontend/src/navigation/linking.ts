import * as ScreenObjects from 'screens';
import { IScreen } from 'type';
const Route = require('route-parser');

export const { linking, screenArr } = (() => {
  const screenUrls = {};
  const screenArr = [];
  const isDev = window.location.host.startsWith("localhost:");
  Object.keys(ScreenObjects).forEach((screenName) => {
    const ScreenObj: IScreen = ScreenObjects[screenName];
    screenArr.push({ name: screenName, component: ScreenObj, title: ScreenObj.routeInfo.title });
    if (!screenUrls[screenName]) {
      let path = ScreenObj.routeInfo.path;
      if (isDev) {
        path = `/:slug/:region/${path}`;
      } else if (!window.location.host.includes("pathfinder-rebuild.devserver.london")) {
        path = `/:region${path}`;
      }
      screenUrls[screenName] = path;
    }
  });
  return {
    linking: screenUrls,
    screenArr,
  };
})();

export const convertRouteToLink = (route, params) => {
  if (!linking[route]) return '';
  var route = new Route(linking[route]);
  return route.reverse(params) || undefined;
}
