module.exports = function (api) {
  api.cache(true);
  return {
    presets: ['babel-preset-expo'],
    plugins: [
      [
        'module-resolver',
        {
          extensions: ['.js', '.ts', '.tsx', '.css'],
          root: ['./src'],
          alias: {
            assets: './src/assets',
            const: './src/const',
            screens: './src/screens',
            helpers: './src/helpers',
            components: './src/components',
            navigation: './src/navigation',
            layout: './src/layout',
            lib: './src/lib',
            store: './src/store',
            type: './src/type',
          },
        },
      ],
    ],
  };
};
