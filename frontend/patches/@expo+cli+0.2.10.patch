diff --git a/node_modules/@expo/cli/build/src/start/index.js b/node_modules/@expo/cli/build/src/start/index.js
index 043be8e..d83af3d 100644
--- a/node_modules/@expo/cli/build/src/start/index.js
+++ b/node_modules/@expo/cli/build/src/start/index.js
@@ -108,7 +108,7 @@ const expoStart = async (argv)=>{
         return _interopRequireWildcard(require("./startAsync"));
     });
     return startAsync(projectRoot, options, {
-        webOnly: false
+        webOnly: options.web
     }).catch(_errors.logCmdError);
 };
 exports.expoStart = expoStart;
