import { FontAwesome5 } from '@expo/vector-icons';
import { UploadFile, TouchField, Image, Col, Row, Text, Dropdown, modal, ImageUploaderModal } from 'components';
import { COLOR } from 'const';
import React, { useMemo, useRef, useState } from 'react';
import { ActivityIndicator, ImageResizeMode, ImageStyle, StyleProp } from 'react-native';
import { Tooltip } from 'react-tippy';
import Store from 'store';
import { TImage } from 'type';

export interface IImageSelectProps {
  value: string;
  resizeMode?: ImageResizeMode;
  imageStyle?: StyleProp<ImageStyle>;
  onChange?: (url: string) => void;
  canRemove?: boolean;
  isButtonsBottom?: boolean;
  entityName?: string;
  shortenUrl?: boolean;
  allowSelectFromDB?: boolean;
}

const DROPDOWN_MENU = [
  { id: 'upload', label: 'Upload', icon: 'upload' },
  { id: 'select-from-db', label: 'Select from DB', icon: 'clone' },
]

const ImageSelect = (props: IImageSelectProps) => {
  const { value, imageStyle, resizeMode, onChange, canRemove = true, isButtonsBottom, entityName, shortenUrl, allowSelectFromDB } = props;
  const uploadRef = useRef<{ showDialog: Function }>(null);
  const [loading, setLoading] = useState(false);
  const { user } = Store.useUserStore();
  const { selectedClient } = Store.useClientStore();

  const handleButtonClick = () => {
    uploadRef.current.showDialog();
  }

  const onImageUploaded = (urls) => {
    if (urls.length === 0) return setLoading(false);
    onChange(urls[0]);
    setLoading(false);
  }

  const onSelectFromDB = (image: TImage) => {
    modal.hide();
    onChange(image.url);
  }

  const handleRemove = () => {
    onChange('');
  }

  const onPressDropdownOption = (option: any) => {
    if (option.id === 'upload') {
      uploadRef.current.showDialog();
    }
    if (option.id === 'select-from-db') {
      modal.show(
        <ImageUploaderModal
          clientId={user?.clientId || selectedClient?.id}
          onSelect={onSelectFromDB}
        />
        , {
          contentStyle: {
            width: "70%",
            minWidth: 300,
          }
        }
      )
    }
  }

  const renderUploadComp = useMemo(() => {
    return (
      <>
        <Image
          source={{ uri: value }}
          style={[{ width: '100%', height: 70 }, imageStyle]}
          borderRadius={10}
          resizeMode={resizeMode || 'contain'}
          emptyText={`Add ${entityName || "image"}`}
        />
        <UploadFile
          ref={uploadRef}
          onUploaded={onImageUploaded}
          onUploading={() => setLoading(true)}
          shortenUrl={shortenUrl}
        />
        {loading &&
          <Col absoluteFill middle backgroundColor={COLOR.MASK}>
            <ActivityIndicator color={COLOR.MAIN} testID='image-uploading' />
          </Col>
        }
      </>
    )
  }, [value, loading, shortenUrl, resizeMode, imageStyle, onImageUploaded])

  const renderImg = useMemo(() => {
    if (allowSelectFromDB) {
      return (
        <Dropdown
          flex1
          dataSet={{ element: 'image-picker' }}
          options={DROPDOWN_MENU}
          onPressOption={onPressDropdownOption}
        >
          {renderUploadComp}
        </Dropdown>
      )
    }
    return (
      <TouchField dataSet={{ element: 'image-picker' }} flex1 onPress={!value ? handleButtonClick : undefined}>
        {renderUploadComp}
      </TouchField>
    )
  }, [value, handleButtonClick, renderUploadComp, allowSelectFromDB])

  if (isButtonsBottom) {
    return (
      <>
        {renderImg}
        {!!value && canRemove &&
          <Row flexWrap={'wrap'} middle>
            <Tooltip
              title="Edit the image"
              trigger="mouseenter"
            >
              <TouchField borderRadius={8} alignSelf={'center'} onPress={handleButtonClick}>
                <Row middle paddingHorizontal={12} paddingVertical={8}>
                  {/* <Text color={COLOR.MAIN}>Edit </Text> */}
                  <FontAwesome5 name='pen' size={16} color={COLOR.MAIN} />
                </Row>
              </TouchField>
            </Tooltip>
            <Tooltip
              title="Delete the image"
              trigger="mouseenter"
            >
              <TouchField borderRadius={8} alignSelf={'center'} onPress={handleRemove}>
                <Row middle paddingHorizontal={12} paddingVertical={8}>
                  {/* <Text color={COLOR.RED}>Delete </Text> */}
                  <FontAwesome5 name='trash' size={16} color={COLOR.RED} />
                </Row>
              </TouchField>
            </Tooltip>
          </Row>
        }
      </>
    )
  }
  return (
    <Row width100p alignItems={'flex-start'}>
      {!!value && canRemove &&
        <Col>
          <Tooltip
            title="Edit the image"
            trigger="mouseenter"
          >
            <TouchField borderRadius={8} alignSelf={'center'} onPress={handleButtonClick}>
              <Row middle paddingHorizontal={12} paddingVertical={8}>
                {/* <Text color={COLOR.MAIN}>Edit </Text> */}
                <FontAwesome5 name='pen' size={10} color={COLOR.MAIN} />
              </Row>
            </TouchField>
          </Tooltip>
          <Tooltip
            title="Delete the image"
            trigger="mouseenter"
          >
            <TouchField borderRadius={8} alignSelf={'center'} onPress={handleRemove}>
              <Row middle paddingHorizontal={12} paddingVertical={8}>
                {/* <Text color={COLOR.RED}>Delete </Text> */}
                <FontAwesome5 name='trash' size={10} color={COLOR.RED} />
              </Row>
            </TouchField>
          </Tooltip>
        </Col>
      }
      {renderImg}
    </Row>
  )
}

export default ImageSelect;
