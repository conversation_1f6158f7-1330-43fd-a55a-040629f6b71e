import React, { useMemo, useRef, useState } from 'react';
import SelectWeb, { components, Props as SelectProps } from 'react-select';
import { Col, IColProps, Text } from 'components';
import { COLOR } from 'const';
import AsyncSelect, { useAsync } from 'react-select/async';
import { isArray, isString, uniqBy } from 'lodash';
import { VarHelper } from 'helpers';

interface ISelectProps extends IColProps {
  required?: boolean,
  value: any,
  onChange: any,
  options?: Array<any>,
  styles?: any,
  noBorder?: boolean,
  isMulti?: boolean,
  innerProps?: SelectProps,
  loadOptions?: any,
  defaultOptions?: Array<any>,
  isDisabled?: boolean,
  placeholderColor?: string,
  [additionProp: string]: any,
}

export interface ISelectTagInputProps extends ISelectProps {
  bonusStyle?: any
  noOptionsMessage?: string
  placeholder?: string
}

const Select = ({ value, onChange, options, noBorder, isMulti, innerProps, loadOptions, defaultOptions, isDisabled, placeholderColor, ...props }: ISelectProps) => {
  const selectRef = useRef<any>(null);
  const height = props.height ? { height: props.height, minHeight: props.height } : {};
  const SelectComponent = !!loadOptions ? AsyncSelect : SelectWeb;
  const handleInputChange = !loadOptions ? undefined : (newValue) => {
    props.onInputChange?.(newValue)
  };

  const onPressPlaceholderBtn = () => {
    selectRef.current?.focus?.();
  }

  return (
    <Col {...props}>
      <SelectComponent
        ref={selectRef}
        value={value}
        onChange={onChange}
        options={options}
        isMulti={isMulti}
        defaultOptions={defaultOptions}
        loadOptions={loadOptions}
        onInputChange={handleInputChange}
        isDisabled={isDisabled}
        styles={{
          control: (style) => ({
            ...style,
            height: isMulti ? 'auto' : 40,
            minHeight: 40,
            borderRadius: 8,
            backgroundColor: 'transparent',
            borderWidth: 1,
            borderColor: noBorder ? 'transparent' : COLOR.BORDER_LIGHT,
            boxShadow: 'none',
            borderStyle: 'solid',
            '&:hover': {
              borderColor: noBorder ? 'transparent' : COLOR.BORDER_LIGHT,
            },
            ...height,
          }),
          input: styles => ({ ...styles, outline: 'none', ...height, }),
          indicatorSeparator: (style) => ({ display: 'none' }),
          placeholder: (style) => ({
            ...style,
            fontSize: 14,
            color: placeholderColor || COLOR.FONT,
          }),
          valueContainer: (provided, state) => ({
            ...provided,
            ...height,
          }),
          indicatorsContainer: (provided, state) => ({
            ...provided,
            ...height,
          }),
          ...props.styles,
        }}
        menuPortalTarget={document.querySelector('body')}
        {...innerProps}
      />
      {/* {!!placeholderBtn &&
        <Text
          textDecorationLine='underline'
          marginLeft={8}
          onPress={onPressPlaceholderBtn}>
          {placeholderBtn}
        </Text>
      } */}
    </Col>
  )
}

export const Select01 = (props: ISelectProps) => {
  return (
    <Select
      noBorder
      backgroundColor={COLOR.GREY_LIGHT}
      borderRadius={4}
      {...props}
    />
  )
}

const addPlaceholderBtn = (opts = []) => {
  return opts.filter(i => !i.isFixed)
    .concat({ label: '+ Add new', value: '0', isFixed: true });
}

export const SelectTagInput = (props: ISelectTagInputProps) => {
  const { value, onChange, bonusStyle, noOptionsMessage, noPlaceholderBtn, placeholder, ...restProp } = props;
  const [inputValue, setInputValue] = useState('');
  const [isFocus, setFocus] = useState(false);

  const _value = useMemo(() => {
    if (!value) return [];
    if (isArray(value)) return value;
    if (!isString(value)) return [];
    return value.split(',').map(i => ({ label: i, value: i }));
  }, [value])

  const loadOptions = (inputValue, callback) => {
    if (inputValue.includes(',')) callback(restProp.options);
    else callback([
      { label: inputValue, value: inputValue },
      ...VarHelper.searchInArray(restProp.options, inputValue),
    ])
  }

  const onSelectChange = (newVal) => {
    onChange?.(newVal.filter(i => !i.isFixed));
  }

  const onInputChange = (newValue: string) => {
    if (newValue?.length > 1 && newValue.includes(',')) {
      const newValues = _value;
      newValue.split(',').forEach(i => {
        if (i && !newValues.some(tag => tag.label === i.trim())) {
          newValues.push({
            label: i.trim(),
            value: i.trim(),
          });
        }
      });
      onChange(uniqBy(newValues, (i) => i.value));
      setInputValue('');
      return;
    }
    setInputValue(newValue);
  }

  return (
    <Select
      noBorder
      {...restProp}
      value={(isFocus || noPlaceholderBtn) ? _value : addPlaceholderBtn(_value)}
      styles={{
        dropdownIndicator: (provided) => ({
          ...provided,
          visibility: 'hidden',
          width: 0,
        }),
        clearIndicator: (provided) => ({
          ...provided,
          visibility: 'hidden',
          width: 0,
        }),
        indicatorsContainer: (provided) => ({
          ...provided,
          width: 0,
        }),
        // valueContainer: (provided) => ({
        //   ...provided,
        //   flexDirection: 'column',
        //   alignItems: 'flex-start',
        // }),
        multiValue: (base, state) => {
          return state.data.isFixed ? { ...base, backgroundColor: 'transparent' } : base;
        },
        multiValueLabel: (base, state) => {
          return state.data.isFixed
            ? { ...base, color: COLOR.MAIN_TEXT, textDecorationLine: "underline" }
            : base;
        },
        multiValueRemove: (base, state) => {
          return state.data.isFixed ? { ...base, display: 'none' } : base;
        },
        ...bonusStyle
      }}
      loadOptions={loadOptions}
      onInputChange={onInputChange}
      onChange={onSelectChange}
      onFocus={() => setFocus(true)}
      onBlur={() => setFocus(false)}
      innerProps={{
        inputValue,
        placeholder: placeholder || '+ Add new',
        noOptionsMessage: () => noOptionsMessage || 'Type new filter and enter',
      }}
      defaultOptions={restProp.options}
    />
  )
}

export default Select;
