import React, { useState } from 'react';
import { Col, IColProps, Row, TouchField } from 'components';
import { TextInput, TextInputProps } from 'react-native';
import { FONT, COLOR } from 'const';
import './Input.css';
import { FontAwesome5 } from '@expo/vector-icons';

interface IProps extends IColProps {
  value?: any;
  onChange?(newValue: any): void;
  inputProps?: TextInputProps;
  placeholder?: string;
  password?: boolean;
}

const Input = ({ value, onChange, inputProps, placeholder, password, ...props }: IProps) => {
  const outlineStyle = {
    outlineColor: COLOR.MAIN,
    outlineWidth: 2,
  }
  return (
    <Col round0 borderThin height={45} overflow='hidden' {...props}>
      <TextInput
        placeholder={placeholder}
        secureTextEntry={password}
        {...inputProps}
        style={[
          {
            width: '100%',
            padding: 10,
            fontFamily: FONT.defaultFont,
            color: '#383838',
            overflow: 'hidden',
            height: '100%',
            // @ts-ignore
            ...outlineStyle,
          },
          inputProps?.style,
        ]}
        value={value}
        onChangeText={onChange}
      />
    </Col>
  );
};

export const InputPassword = ({ value, onChange, inputProps, placeholder, password, ...props }: IProps) => {
  const [isShowing, setShowing] = useState(false)
  const outlineStyle = {
    outlineColor: COLOR.MAIN,
    outlineWidth: 2,
  }
  return (
    <Row round0 borderThin height={45} overflow='hidden' {...props}>
      <TextInput
        placeholder={placeholder}
        {...inputProps}
        secureTextEntry={!isShowing}
        style={[
          {
            width: '100%',
            padding: 10,
            fontFamily: FONT.defaultFont,
            color: '#383838',
            overflow: 'hidden',
            height: '100%',
            // @ts-ignore
            ...outlineStyle,
          },
          inputProps?.style,
        ]}
        value={value}
        onChangeText={onChange}
      />
      <TouchField p1 onPress={() => setShowing(s => !s)}>
        <FontAwesome5 name={isShowing ? "eye" : "eye-slash"} size={14} light color={COLOR.MAIN} />
      </TouchField>
    </Row>
  );
};

export const Input02 = (p: IProps) => {
  return (
    <Input
      height={30}
      backgroundColor={COLOR.GREY_LIGHT}
      borderColor='transparent'
      {...p}
    />
  );
}

export default Input;
