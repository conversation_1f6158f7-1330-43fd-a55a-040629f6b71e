import React, { useCallback, useMemo, useState } from 'react';
import { Col, Text, Table, Row, TouchField, Image, CheckBox, modal } from 'components';
import { COLOR } from 'const';
import ExportHelper from 'helpers/ExportHelper';

const ConfirmExportModal = ({ pathfinders }: any) => {
  const [selectedIds, setSelectedIds] = useState([]);
  const selectedAll = selectedIds?.length && selectedIds.length === pathfinders.length;

  const onSubmit = useCallback(async () => {
    try {
      const selectedPathfinder = pathfinders.filter(i => selectedIds.includes(i.id));
      ExportHelper.saveDataToFile(selectedPathfinder);
    } catch (error) {
      alert(error?.message || error);
    }
  }, [pathfinders, selectedIds])

  const onSelectItem = useCallback((id) => {
    if (selectedIds.includes(id)) {
      setSelectedIds(selectedIds.filter(i => i !== id))
    } else {
      setSelectedIds(selectedIds.concat(id))
    }
  }, [selectedIds])

  const onSelectAll = useCallback(() => {
    if (selectedAll) {
      setSelectedIds([])
    } else {
      setSelectedIds(pathfinders.map(i => i.id))
    }
  }, [selectedAll, selectedIds, pathfinders])

  const data = useMemo(() => {
    if (!pathfinders?.length) return [];
    return pathfinders.map(i => ({
      logo: <Image source={{ uri: i.logo }} style={{ width: 70, height: 70 }} resizeMode='contain' />,
      name: i.name,
      elements: i.elements?.map(el => el?.name).join(', '),
      totalStatements: i.statements.length,
      selected: <CheckBox value={selectedIds.includes(i.id)} onChange={() => onSelectItem(i.id)} />
    }))
  }, [pathfinders, onSelectItem, selectedIds])

  return (
    <Col round1 bgWhite shadow p2>
      <Text fontSize={16} semiBold mb2>Confirm Export</Text>
      <Text mb1>Select pathfinder to export:</Text>
      <Table
        columns={[
          { key: 'selected', title: '', width: 50, padding: 10 },
          { key: 'logo', title: 'Logo', width: 95, padding: 10 },
          { key: 'name', title: 'Name', flex: 1 },
          { key: 'elements', title: 'Elements', flex: 1 },
          { key: 'totalStatements', title: 'Total Statements', flex: 1 },
        ]}
        data={data}
        style={{ margin: 20 }}
        minWidthRequired={500}
      />
      <Row justifyContent={'space-between'} mt2>
        <TouchField
          height={40}
          middle
          borderColor={COLOR.MAIN}
          borderRadius={20}
          borderWidth={1}
          onPress={onSelectAll}
          m0
          ph2
        >
          <Text color={COLOR.MAIN}>{selectedAll ? 'Un-select all' : 'Select all'}</Text>
        </TouchField>
        <Row>
          <TouchField
            height={40}
            width={80}
            middle
            borderColor={COLOR.MAIN}
            borderRadius={20}
            borderWidth={1}
            onPress={() => modal.hide()}
            m0
            ph2
          >
            <Text color={COLOR.MAIN}>Close</Text>
          </TouchField>
          <TouchField
            height={40}
            ph2 m0 middle
            borderColor={COLOR.MAIN}
            borderRadius={20}
            borderWidth={1}
            backgroundColor={COLOR.MAIN}
            disabled={!selectedIds?.length}
            onPress={onSubmit}
          >
            <Text color={'white'}>Export</Text>
          </TouchField>
        </Row>
      </Row>
    </Col>
  );
};

export default ConfirmExportModal;
