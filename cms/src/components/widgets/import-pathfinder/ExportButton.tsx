import React, { useCallback, useState } from "react";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { TouchField, modal } from "components/elements";
import { COLOR } from "const";
import Store from "store";
import ConfirmExportModal from "./ConfirmExportModal";
import { ActivityIndicator } from "react-native";

const ImportButton = () => {
  const [loading, setLoading] = useState(false);
  const onSubmit = useCallback(async () => {
    try {
      setLoading(true);
      const selectedClient = Store.useClientStore.getState().selectedClient;
      const res = await Store.Api.Pathfinder.export({
        clientId: selectedClient?.id,
      });
      if (res?.data?.data?.length) {
        modal.show(
          <ConfirmExportModal pathfinders={res.data.data} />
        );
      }
    } catch (error) {
      alert(error?.message || error);
    } finally {
      setLoading(false);
    }
  }, []);

  return (
    <TouchField
      width={36} height={36}
      borderRadius={18} middle
      onPress={onSubmit}
      dataSet={{ element: 'export-pathfinder' }}
    >
      {loading
        ? <ActivityIndicator size='small' color={'white'} style={{ marginRight: 5 }} />
        : <MaterialCommunityIcons name='database-export' size={28} color={COLOR.MAIN} />
      }
    </TouchField>
  )
}

export default ImportButton;
