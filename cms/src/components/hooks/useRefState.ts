import { useRef, useState } from 'react';

export const useRefState = <T>(initialValue : T = undefined) : [T, () => T, (v: T | Function) => void] => {

  const [value, setValue] = useState(initialValue)
  const valueRef = useRef(initialValue);

  const valueInRender = value;
  const getLatestValueToUseInFunction = () => {
    return valueRef.current;
  };
  const setValue2 = (newValueOrFunc) => {
    const newValue = typeof newValueOrFunc === 'function' ? newValueOrFunc(valueRef.current) : newValueOrFunc;
    valueRef.current = newValue;
    setValue(newValue);
  };

  return [
    valueInRender,
    getLatestValueToUseInFunction,
    setValue2,
  ];
}