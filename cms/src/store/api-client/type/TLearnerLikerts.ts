
export type TLearnerLikerts = {
  id: string;
  clientId?: string;
  learnerId?: string;
  pathfinderId?: string;
  elementId?: string;
  pathfinderName?: string;
  likerts?: {
    [statementId: string]: {
      statement: string;
      likert: number;
      learnings: { name: string, url: string }[]
    }
  }
  data?: any;
  deletedAt?: string;
  // db fields
  createdAt?: string;
  updatedAt?: string;
}
