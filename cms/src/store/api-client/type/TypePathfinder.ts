
export type TPathfinder = {
  id: string;
  clientId: string;
  orderIndex?: number;
  name: string;
  logo?: string;
  url?: string;
  data?: any;
  welcomeInstruction?: string;
  additionalInstruction?: string;
  completionInstruction?: string;
  listStatementTitle?: string;
  listStatementSubtitle?: string;
  listLearningTitle?: string;
  likertScaleTitle1?: string;
  likertScaleTitle2?: string;
  likertScaleTitle3?: string;
  elementsTitle?: string;
  disableElementsFilter?: boolean;
  deletedAt?: string;
  sendEmail?: boolean;
  additionalRecipients?: string;
  emailSubject?: string;
  emailContent?: string;
  emailFooter?: string;
  hidePathfinderLogo?: boolean;

  // db fields
  createdAt?: string;
  updatedAt?: string;
}
