import * as React from "react"
import { SVGProps } from "react"

const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={23}
    height={23}
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
    {...props}
  >
    <path fill="url(#a)" d="M0 0h23v23H0z" />
    <defs>
      <pattern
        id="a"
        patternContentUnits="objectBoundingBox"
        width={1}
        height={1}
      >
        <use xlinkHref="#b" transform="scale(.00195)" />
      </pattern>
      <image
        id="b"
        width={512}
        height={512}
        xlinkHref="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAgAAAAIACAYAAAD0eNT6AAAACXBIWXMAAA3XAAAN1wFCKJt4AAAF0WlUWHRYTUw6Y29tLmFkb2JlLnhtcAAAAAAAPD94cGFja2V0IGJlZ2luPSLvu78iIGlkPSJXNU0wTXBDZWhpSHpyZVN6TlRjemtjOWQiPz4gPHg6eG1wbWV0YSB4bWxuczp4PSJhZG9iZTpuczptZXRhLyIgeDp4bXB0az0iQWRvYmUgWE1QIENvcmUgNy4yLWMwMDAgNzkuNTY2ZWJjNWI0LCAyMDIyLzA1LzA5LTA4OjI1OjU1ICAgICAgICAiPiA8cmRmOlJERiB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiPiA8cmRmOkRlc2NyaXB0aW9uIHJkZjphYm91dD0iIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtbG5zOmRjPSJodHRwOi8vcHVybC5vcmcvZGMvZWxlbWVudHMvMS4xLyIgeG1sbnM6cGhvdG9zaG9wPSJodHRwOi8vbnMuYWRvYmUuY29tL3Bob3Rvc2hvcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RFdnQ9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZUV2ZW50IyIgeG1wOkNyZWF0b3JUb29sPSJBZG9iZSBQaG90b3Nob3AgMjMuNCAoTWFjaW50b3NoKSIgeG1wOkNyZWF0ZURhdGU9IjIwMjItMDgtMTZUMTA6MzU6NDErMDE6MDAiIHhtcDpNb2RpZnlEYXRlPSIyMDIyLTA4LTE2VDEwOjM2OjAxKzAxOjAwIiB4bXA6TWV0YWRhdGFEYXRlPSIyMDIyLTA4LTE2VDEwOjM2OjAxKzAxOjAwIiBkYzpmb3JtYXQ9ImltYWdlL3BuZyIgcGhvdG9zaG9wOkNvbG9yTW9kZT0iMyIgeG1wTU06SW5zdGFuY2VJRD0ieG1wLmlpZDowOTVjYTE1Yi04M2RiLTRiMTktODhmNS0xM2ZjMzIyM2NhNTIiIHhtcE1NOkRvY3VtZW50SUQ9ImFkb2JlOmRvY2lkOnBob3Rvc2hvcDo4NjNlMzgxOC01NGMxLWQwNGQtOGZhNi05ZWQ4MjdlMmI3MWUiIHhtcE1NOk9yaWdpbmFsRG9jdW1lbnRJRD0ieG1wLmRpZDo0ZTFkODMwNi0zMWMxLTRmZTEtOTA3MC1mNWYyM2RhMDBkOTEiPiA8eG1wTU06SGlzdG9yeT4gPHJkZjpTZXE+IDxyZGY6bGkgc3RFdnQ6YWN0aW9uPSJjcmVhdGVkIiBzdEV2dDppbnN0YW5jZUlEPSJ4bXAuaWlkOjRlMWQ4MzA2LTMxYzEtNGZlMS05MDcwLWY1ZjIzZGEwMGQ5MSIgc3RFdnQ6d2hlbj0iMjAyMi0wOC0xNlQxMDozNTo0MSswMTowMCIgc3RFdnQ6c29mdHdhcmVBZ2VudD0iQWRvYmUgUGhvdG9zaG9wIDIzLjQgKE1hY2ludG9zaCkiLz4gPHJkZjpsaSBzdEV2dDphY3Rpb249InNhdmVkIiBzdEV2dDppbnN0YW5jZUlEPSJ4bXAuaWlkOjA5NWNhMTViLTgzZGItNGIxOS04OGY1LTEzZmMzMjIzY2E1MiIgc3RFdnQ6d2hlbj0iMjAyMi0wOC0xNlQxMDozNjowMSswMTowMCIgc3RFdnQ6c29mdHdhcmVBZ2VudD0iQWRvYmUgUGhvdG9zaG9wIDIzLjQgKE1hY2ludG9zaCkiIHN0RXZ0OmNoYW5nZWQ9Ii8iLz4gPC9yZGY6U2VxPiA8L3htcE1NOkhpc3Rvcnk+IDwvcmRmOkRlc2NyaXB0aW9uPiA8L3JkZjpSREY+IDwveDp4bXBtZXRhPiA8P3hwYWNrZXQgZW5kPSJyIj8+mL2KIgAAHjRJREFUeJzt3dtyHDmSBFBoTP//y9wHLVssqpJ1yQQQDpxjtjZjOy0yM3BxV0kt/fr4+GgAQHmvBPavR//A7xMPAgD0987P1D9/zGER+N97zwIADHD2Y/qPo6+hAABATVf+Gv0/X0sBAIB6evwGvZuvqQAAQC09f3f+f19bAQCAOob9q3kKAADUMCr8P1pTAACgguF/KI8CAABzzfgT+T4UAACYZ9ofx6sAAMAcU/8sfgUAAMab/hfxKAAAMNb08G9NAQCAkUqEf2sKAACMUib8W1MAAGCEUuHfmgIAAL2VC//WFAAA6Klk+LemAABAL2XDvzUFAAB6KB3+rbVfCgAAXKt6+LfWfAIAAFdKCP9frSkAAHCVmPBvTQEAgCtEhX9rCgAAnBUX/q0pAABwRmT4t6YAAMC7YsO/NQUAAN4RHf6tKQAA8Kr48G9NAQCAVywR/q0pAADwrGXCvzUFAACesVT4t6YAAMAjy4V/awoAAPxkyfBvTQEAgCPLhn9rCgAA3LN0+LemAADAd8uHf2sKAAB8tUX4t6YAAMCnbcK/NQUAAFrbLPxbUwAAYLvwb00BAGBvW4Z/awoAAPvaNvxbUwAA2NPW4d+aAgDAfrYP/9Za+937GwBw6GsQdb/waa0J//8oAADXOBss7/x4peE1wv8LBQDgedUC5Oh5FIN/VVu7e4aumwIAcCwhNO6599w7l4KEdRy+PgoAwF8JQfGu7++2SyFIWNMpa6EAADtLCIdedvgNiAnrO232CgCwo4RgGGnFMpCwxlNnrQAAu0gIhApWKAMJaz19tgoAsLKEIKjsc37Tw+oFCWteYp4KALCihBBIkvKpQMK6l5mfAgCsJCEA0lX9VCBh7UvNTAEAVpBw+a+mUhFIWP8Kc7qhAACpEi79HcwuAgn7oFz4t+ZvAwQyJVz6u/lo49clYR+UDP/WfAIAZEm48Hc36hOBhL1QNvxb8wkAkGHGzy45p+d6JeyF0uHfmk8AgNoSLnqO9fg0IGFPlA//1nwCANSVcNHznKs+wUnYExHh35oCANTj4/51nVnXhD0RE/6tKQBALQmXPOe8U/AS9kVU+LemAAA1+Fn/fp5d74R9ERf+rSkAwHwJFzx9PFr7hL0RGf6tKQDAXAkXPH0dffqTsDdiw781BQCYw0f+fPdx8N+rig7/1vw5AMB4CZc7c6Tsjfjwb80nAMBYKRc8HFki/FtTAIBxhD/plgn/1hQAYAzhT7qlwr81BQDoT/iTbrnwb00BAPoS/qRbMvxb828BAH0I/uuDw0zHWzb8W1MAgOvtFlSjQuLo++w271GWDv/WFADgWjuEUbVg+P48O6xBb9XWuAsFALjKysGTFAgKwTlJa32KAgBcYcWQWSUIvr7Hiut0pVXW/CkKAHDWSqGyegAoA8dWX/t/KADAGauEyHaXf/v7zqus4Rk7rr8CALxtheDY8uL/ZvcisO0eUACAd6SHxbaX/g92LAJb7wMFAHhVckBsfeE/aZcisP1eUACAV6SGwvaX/RtWLgL2Q/N3AQDPSwyCX81lf9Zq81vtfd6mAADPSA1/rrFKkVrhHS6jAACPpIX/KmFVUfJck5+9CwUA+Eli+NNXYsFKe94hFADgSFL4J4ZSOvMOpwAA96SFP3OkzD5pPw+jAADfJV2WKQG0spRPX5L29RAKAPBV0iWZEDo7sR5hFADgk/DnrOrrkrTHu1MAgNZyLsaUj5t3Vn19UvZ6dwoAkHIhVg8W/qq+Vil7visFAPaWchFWDxT+Zc2KUwBgX8Kf3iqvXcr+70YBgD2lXH6VA4TnVF7DlHPQhQIA+0m59CoHB6+xlgUpALAX4c8sVdc05UxcTgGAfaRcdFWDgvOsbSEKAOxB+FNFxTVOOR+XUgBgfSmXW8VgoI+Ka51yTi6jAMDaUi61ioFAX9Z8MgUA1iX8qa7a2qecmUsoALCmlIusWgAwnj0wiQIA6xH+pKm0F1LOz2kKAKwl5fKqdOHDlhQAWIfwJ1mlfZFylk5RAGANKRdWpUueeuyPgRQAyCf8WUmVfZJyrt6mAEC2lEuqyqUO/D8FAHIJf1ZVZc+knLG3KACQKeViqnKRk8fe6UwBgDzCHzhNAYAswp+dVNhHKWfuZQoA5Ei5iCpc2qzDfupEAYAMwh/mSTl/L1EAoL6Uy0f404u91YECALUJf6ALBQDqEv7w1+x9lnIen6YAQE0pl83sSxl4kwIA9Qh/uM+eu5ACALUIf6gr5Xw+RQGAOlIuF+HPTPbfRRQAqEH4A0MpADCf8IfXzNyLKef1IQUA5kq5TIQ/LEYBgHmEPzCNAgBzCH84x9486ffsB4ANCf9r3Jtj9WdmDR9tgb2mAMBYwv99z8zu6z9T8R2gDL8EAOMI//d8tPdm9+6PI0e1vRpFAQC+qnahXhHgigDcoQDAGAkBtGL49/x6EE0BAFpbP/x7f132E7+XFADor/pFsUv4j/r6jFVt/8ZQAGBvu16eSgDbUwBgXxXDf2QwKwFsTQGAvqqGTMXwn6Hq+kB3CgDsp2r4zwpjJSBf1T1dmgIAe3FR3qcE8I7ofaMAwF6iL6zOzIatKACwH0F3zGzYhgIAfVX9yF3QHTMbtqAAwL4E3TGzYXkKAOxN0B0zmyxVP20rSwEABN0xs2FZCgD0l/AzE0F3zGxYkgIAfBJ0x8yG5SgAMEbCpwCtzQ266jNSAliKAgDjVA+4T4LumNmwDAUAxlICfpYwHyWAJSgAMF5CyLUm6H5iNsRTAGAOJeCY2ZAkdh8oADCPoDtmNtCZAgBzCbpjZgMdKQAwn6A7ZjZUl7JH/6EAQA0pl4gScEwJIIoCAHUIumNmAxdTAKAWQXfMbOBCCgDUI+iOmQ1cRAGAmgTdMbOBCygAUJegO2Y2cJICALUJumNmAycoAFCfoDtmNvAmBQAyCLpjZkNr5vsyBQByCLpjZgMvUgAgi6A7ZjbwAgUA8gi6Y2YDT1IAIJOgO2Y2jJKy1+5SACBXyuWjBBxTAphGAYBsgu6Y2ezDDN+gAEA+QXfMbOCAAgBrEHTHzAbuUABgHYLumNnANwoArEXQHTObNZnXmxQAWI+gO2Y2XCVlLx1SAGBNKZeTEnBMCaArBQDWJeiOmc0azOcEBQDWJuiOmQ1bUwBgfYLumNnwjpR98yMFAPaQcmEpAceUgFvmcZICAPsQdMfMhu0oALAXQXfMbNiKAgD7EXTHzCbDzPdP2SMPKQCwp5RLTAk4tnsJ4CQFAPYl6I6ZTV07vnMXCgDsTdAdMxuWpgAAgu6Y2dQy+z1T9sNTFACgtZyLTQk4NjscCaMAAJ8E3TGzmW/2u6XsgacpAMBXKZecEnBsdlASQgEAvhN0x8xmjtXepwQFALhH0B0zm/2krPlLFADgSMqlpwSsTZHpRAEAfpISdErAfcKTQwoA8EhC0LWmBKyoQoFZdo0VAOAZKZegEvCvCiH6jtTnjqEAAM+qHnSflACusvS6KgDAK1IuRCUgm5/9D6AAAK9KCTol4K+kQK3yrFXX8jIKAPCOlMtRCYADCgDwrpSgUwJy+Nn/QAoAcEbKRakE1Fcl/LehAABnpQSdElCX8J9AAQCukBJ0u5aACs9wpFr4V57VpX7PfgDYxKNLboVL51erd5nf89HGzztlNqNVm8kK5/BpPgGAfj6+/N+V/2xlKRforp8EwH8UAOjjTMCkF4GUoFMC5qq2x7dbGwUArnVleFe7IF+RcpnuUAIqrkXy3l6GAgDX6XGpJV+UFYPnnh1KQCUV9/SW66EAwDV6XmoVL8xnpVysq5aAavNP3svL+fXxYT14y72NU+2yGWXUIUqeb8pFM2PGvWZTbb9U3QPV5jSMAsArXt0sOxyskQcofZ4pl80KJaDaXqm69tXmNJRfAuAZ7/7GtvTfzf7I6HdLn2XKZZv+ywHV5py+b5elAPCTqwJ8xQtgxXcaoVo4HZlVAs7M5+yP76HyOak2q+EUAI5cfXArXwSvWuldZki5eGet86tBXjH4W6t9TirOazi/B4B7em6K9INX4cCkz/BThVk+Y5V5j1R9ba1p8wkA/+p9cKtfDD9JfvaKUi5h6/6a6vNK2XfdKQB8NergVr8g7kl85gQpl7H1f071OaXstyEUAD75He3Hkp41UcqlbB/8rPp8UvbZMAoArc07uNUvjNYynnEFKZez/XCfuQRSAJh9cGd//59UfrYVKQGZEuaRsreGUgD2VuXgVnmOryo+0w5SLmr744+EOaTsqeEUgH1VO7iVnqfSs3y3w2WW8o6V98kICe+fspemUAD2VPXgVniuCs9AzsW9635JeO+UPTSNArCf6gd35vNVn81uUi7w3fZNwvum7J2pFIC9JBzc1uY8Z8JsdrzUUt45Yf9cIeE9U/bMdArAPhIO7lcjnzdtNrtJudBX30cJ75eyV0pQAPaQcHDvGfHcKbPZ/WJLef+U/fSqVd9rawrA+tIPbs/nT5lNSvj1ljKHlH31rJT3SdkfZSgAa0s5uI/0eI+U2bjUbqXMI2V/PZLyHin7ohQFYF0pB/dZV75PymxcavelzCVlnx1Jef6U/VCOArCmlIP7qiveK2U2LrWfpcwnZb99l/LcKfugJAVgPSkH911n3i9lNi6156TMKWXffUp53pT1L0sBWEvKwT3rnfdMmY1L7TUp80rZfynPmbLupSkA60g5uFd55X1TZuNSe0/K3Krvw+rP9yllvctTANaQcnCv9sx7p8zGpXZOyvyq7seqz/VdyjpHUADypRzcXn56/5TZuNSukTLHavuy2vMcSVnfGApAtpSD29u9OaTMxqV2rZR5VtmfVZ7jkZR1jaIA5Eo5uKN8HPz3ylxqfaTMdfY+nf39n5WynnEUgEwpB3e0j5YzG5daXynznbVfnRMUgEApB5djLrUxUuY8+kyn3CEp6xdLAciScnA55lIbK2Xeo852yh2Ssm7RFIAcKQeXYy61OVLm3vuMp9whKesVTwHIkHJwOeZSmytl/r3OesodkrJOS1AA6ks5uBxzqdWQsg5Xn/mUOyRlfZahANSWcnA55lKrJWU9rjr7KXdIyrosRQGoy8HNZzY1pazL2TvAHcKPFICa0g6uA/wvM6ktZX3evQvS7hAmUADqST24DvJfZpEhZZ1evRNS7xAGUwBqST+4DrQZpElZr2fvhvQ7hIEUgDpWObg7H+yd3z1Zyro9uiNWuUMYRAGoYbWDu+MB3/GdV5Kyfkd3xWp3CAMoAPOtenB3Oug7vevKUtbx+52x6h1CZwrAXKsf3B0O/A7vuJOU9fz49p/Vpcx1KwrAPLsc3JUP/srvtrOUdd3lDqETBWCO3Q7uihfAiu/EX9b3GuZYmAIw3m7h3+vrzbTSu3DMOp9jfsUpAGPtGv69v+5IK7wDz7Pe7zG3AArAOLuH/6iv31Pys/M+6/4a8wqhAIwh/Od8nyslPjPXsf7PMacgCkB/wr/G9zsj6Vnpxz74mfmEUQD6Ev41v++rUtaR/lL27GjmEkgB6CclNGYf3Nnf/1kp60l/KXt2FPMIpQD0kRIWVQ5uled4JGVd6S9lz/ZmDsEUgOulhES1g1vteY6krC/9pezZXnZ//3gKwLVSwqHqwa36XN+lrDP9pezZq+363ktRAK6TEgrVD2715/uUst70l7Jnr7Lb+y5LAbhGShikHNyU50xZd/pL2bNn7fKeW1AAzksJgbSDm/K8KetPfyl79l2rv992fn18uL9OSBle8sE147X8tJ6rzDBlz75ilbXhCwXgfSmDW+HgmnW2d9YvfZYpe/YZ6WvBAb8E8J6Uw73KwU15j5R9McpHe38mZ35sBSl79pFV3oM7FIDXpVxKqx3clPdJ2R+9XTWH5Hmm7Nkj6c/PAwrAmlY9uCnvlRxaV7j6/ZPnmbJnv0t9bl6gALwm4SJa/eCmvF/CXumh13snzzNlz35Ke17epAA8L+EC2uXgprxnwp65Uu/3TZ5nyp5NeU4uoAA8J+Hi2e3gprxvwt65wi7veUb1PVv9+biYAvBYwsW268FNee+EPXTGyPdLn2XVPVv1uehIAfhZwmWz+8FNef+EvfSOVd+rp2p7ttrzMIgCcCzhYnNw/0iZQ8KeesVq7zNSlT1b5TmYQAHI5eDeSpnHKqE58z1WmeHsPTv7+zOZAnBf9QvGwb0vZS7V99cj6c9fyaw9m3JW6EgB+Ff1y83B/VnKfKrvsyOpz13Z6D2bckbo7PfsByim+uVW+eAezW7GM/9q9deytT/PWHlNv0uYaapRezZpv9GZApCj4sF95sL6/s+Meg8l4FoJs0zXe88m7DMG8ksAf1W+4Kod3LN/y9so1eZ2pPLea63+862k155NOQsMpAD8UfmCq3Zwr5jVyL/qtdr8jlTdg1Wfa2VX79mUM8BgCkDtC67awU39W96qzfFItb1Y7Xl2csWe/XXR12FRCkBd1Q5u+t/yVm2eR6qEbpXnuCdlLc96N8AFP0/59fFR+Zx3V/Xlqx3elX53ctU1/27mHqg+o2rnY7R767P7THiDfwugnl0P8qjfDe/fDnj8falt1zuCi+38SwAVL7qKB3vFv+mt4pzvGb1HK56J71LWDsrbtQBUvOhcbH8oAbdGzaPimfguZc0gwq4FoJqqF9usUFACbvWeR0L4AxfbsQBUu+xSQmg0JeBW+r+FcVbKOkGMHQsAOZSAW6l/DsNZKesDUXYrANUuPBfbY0rAravmUe0sHElZF4izWwGoxMX2PCXg1tl5CH9gqwJQ6dJzsb1OCbiV8JcxnZGyDhBrpwJQhYvtfUrArVfnIfyB/+xSAKpcfC6285SAW8/Oo8oZeCRl7hBvlwLAWpSAW4/mIfyBf+xQAKpcfi63aykBt47mUWX/P5IyZ1jGDgWggtTLrfpzKwG3vs9D+AOHVi8AKRcg71MCbn18+8/qUuYKy1m9AFSQfsElPL8ScEv4Aw+tXAAqXIIuuHGUgCzmCJOtXAC4TsplrQRkMD8oQAHoZ7VLLuV9lIDazA2KWLUAVPj4f0Upl7cSUJN5QSGrFoDZVr7oUt5NCajFnKAYBYB3pFzmSkAN5gMFrVgAZn/8v8tll/KeSsBc5gJFrVgAGCflclcC5jAPKEwBuNaOF17KOysBY5kDFLdaAZj98f+uUi57JWCM3d8fIqxWAGba/dJLeX8loK9d3xviKABcKeXyVwL62O19IdpKBWDmx/8uvr9SZqEEXGuX94RlrFQAqCMlDJSAa6z+frAkBYBeUkJBCThn1feC5SkA57kAj6XMRgl4z2rvA1tZpQD41//qSgkJJeA1q7wHbGuVAkBtKWGhBDwn/fmBpgCc5SJ8XsqslICfpT438I0CwEgp4aEE3Jf2vMAPFABGSwkRJQBY2goFYNZvAHRxvy9ldkrALb/ZFhayQgEgk9C7ZR7AUAoAMwm9W+YBDKMAMJvQu2UewBAKwHtSLukUKfNUAm4pARAsvQC4gNYh9G6ZB9BVegFgLULvlnkA3SgAVCP0bpkH0IUCQEVC75Z5AJdTAKhK6N0yD+BSCgCVCb1b5gFcRgF4XcolvIqUeSsBt5QAKE4BIIHQu2UewGkKACmE3i3zAE5RAEgi9G6ZB/A2BYA0Qu+WeQBvUQBIJPRumQfwMgWAVELvlnkAL1EASCb0bpkH8DQFgHRC75Z5AE9RAFiB0LtlHsBDCgCrSAm9UVLmoQTAJAoAK0kIvZGBlzCP1pQAmEIBYDUJoacE/EsJgMEUAFaUEnqjpMxDCYCBFABWlRJ6o6TMQwmAQRQAVpYSeqOkzEMJgAEUAFZXNfRmhVzVeQCDKQDsQOjdSpiHTwGgMwWAXSSE3kjmAZtTAF7nZya5hN4t84CNKQDsRujdMg/YlALAjoTerarz8GkbdKQAsKvZoTf7+39X7XmAzhQAdib0bpkHbCS9ALiwOMse+stH7rCR9AIwi4tyLaNLQMXSYU/DZhQA+KNiKI9SNfx3XhPoTgGAv0YETrVQqxr+QGcKANzqGdDCHyhDAXify3NdPYJa+L+m2rxgOSsUABcFPVy5r6rt0erhDwzwe/YDQGGfwf1uYFYL/tYywr/i3GA5CgA89moRqBpgCeEPDKIAnPPR6l72XC95rVPCP3nGEGWF3wMA/Ez4A/9YpQC4OOA+4Q/ctUoBmCnlgmU/KXtT+MMECgCsSfgDP1qpAMy8SFIuW/aQsh+FP0y0UgEAhD/wJAUA1iH8gacpANdJuXxZU8r+E/5QxGoFwOXCjoQ/8LLVCsBsKRcx60jZc8IfilEAIJfwB962YgGYfdmkXMpkS9lns88jcGDFAgCrE/7AaQpAHykXNHlS9pbwh+JWLQAuH1Yk/IHLrFoAKki5rMmQsp+EP4RYuQBUuIhSLm1qS9lHFc4c8KSVCwCsQPgDXSgA/aVc4NSTsneEPwRavQBUuZhSLnLqSNkzVc4Y8KLVCwAkEv5AdzsUgCqXVMqlzlwp+6TKuQLetEMBqCTlcmeOlP0h/GEBuxSAShdWyiXPWCn7otJZAk7YpQBAZcIfGG6nAlDp8kq58OkvZS9UOj/ABXYqANWkXPz0k7IHhD8saLcCUO0iSwkArpey9tXODHCR3QpARSlBwHVS1lz4w8J2LAAVL7WUQOC8lLWueE6AC+1YAFqrebmlBAPvS1njiucDuNiuBaCqlIDgdSlrK/xhEzsXgKoXXUpQ8LyUNa16JoAOdi4AlaUEBo+lrKXwh83sXgAqX3opwcGxlDWsfA6ATnYvAK3VvvxSAoR/paxd5f0PdKQA1JcSJPyVsmbCHzamAPxR/SJMCRRy1qr6ngc6UwD+qn4hpgTLrj5azhpV3+vAAArAreoXY0rA7CZpXarvcWAQBSBPUtjsIGk9hD/wHwXgXwmXZNLHzStLWoOEfQ0MpADcl3JZJgXQapJmn7KfgYEUgHxJQbSCtE9fhD9wlwJwX9IF31peKKVKm7HwBw4pAP9Ku+S/Sn72yhILlvAHfqQA3Eq75O9JDKvKEmcp/IGHfs9+gEISL/qffL6PMHhP6n6w3sBTfALwR+pl/4yV362H5E9QhD/wNJ8A5F72r/BpwGPp+8DaAi/ZvQCkX/qvUgT+tcIesJ7Ay3YuACtc/O9SBNZZ/53XEDhh1wKwyuV/1tc57BIkK639LmsGdLBjAVgpAK608qcCK675iusEDLRbAVgxCK62yqcCK6918roARexUAFYOhF6+z6x68OywxtXXAAixSwHYIRhGqFYIdlvX2fMGFrJDAdgtJEY6mu2VQWX9/hD+wKVWLwDCYw5zv47gB7pY+Y8CFkKkE/5AN6sWAOFPOuEPdLViARD+pBP+QHerFQDhTzrhDwyxUgEQ/qQT/sAwqxSAhPD/1VzwHLM3gKFWKAAp4X/vv4NiCEyRXgDSwv/r/8+ljz0ATJNcAFLD/5X/nTUpgMB0qQVghfB/9Z9jDdYbKCGxAKwU/l//ecGwPmsMlJFWAFYM/6t+LHUpeEA5SX8Z0Orh//1rJLwvPxP6QFkpnwAkhOHVl72fNWazdkBpCQVgx/Af9bW5nuIGRKj+SwC7h//375Ewj10JfSBK5U8AEsJu9KXvZ5c1WRMgTtVPAIT/c987YU6rEvpAtIoFICHUqlz+isB4VdYe4JRqBSAhyCoGgCLQX8V1B3hbpQKQEF7VQ+Dr8yXMM0H1NQd4S5UCkBBWaUHgU4H3pa01wMsqFICEgEoOBJ8KPCd5jQFeNrsAJATSSsGgDNxaaW0BXjKzACQE0MoBsWsZWHlNAZ42qwAkBM5OQfH9XRPW51k7rSPA02YUgIRw2T007r1/wrq1Zu0AnjK6ACSEiAC572guM9bUGgGcNLIACP81vTuzjxM/FoCTRv1lQMKf78wbYKIRBUD4A0AxvQuA8AeAgnoWAOEPAEX1KgDCHwAK61EAhD8AFHd1ARD+ABDgygIg/AEgxFUFQPgDQJArCoDwB4AwZwuA8AeAQGcKgPAHgFDvFgDhDwDB3ikAwh8Awr1aAIQ/ACzglQIg/AFgEc8WAOEPAAt5pgAIfwBYzKMCIPwBYEE/FQDhDwCLOioAwh8AFnavAAh/AFjc9wIg/AFgA18LgPAHgE18FgDhDwAbOfvXAY8i/AHgQv9r9X/2L/wB4GLVPwEQ/gDQQeUCIPwBoJOqBUD4A0BHFQuA8AeAzqoVAOEPAANUKgDCHwAGqVIAhD8ADFShAAh/ABhsdgEQ/gAwwcwCIPwBYJL/tTlBLPwBYKIZnwAIfwCY7LMAjApl4Q8ABYz8BED4A0ARXwtAz4AW/gBQyPdPAHoEtfAHgGLu/RLAlYEt/AGgoKPfA/CrnQ9v4Q8ARf1+8L9/hvjHC19T8ANAcf8H7Ppdt/xYR1sAAAAASUVORK5CYII="
      />
    </defs>
  </svg>
)

export default SvgComponent
