import { Col, Text, CMSLayout, Table, Row, TouchField, modal, modalConfirm, AdminDetailModal } from 'components';
import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { IScreen, TUser } from 'type';
import Store from 'store';
import { useNavFunc } from 'navigation';
import { COLOR } from 'const';
import { FontAwesome5 } from '@expo/vector-icons';
import { Tooltip } from 'react-tippy';

const ListGlobalAdmin: IScreen = () => {
  const { navigate } = useNavFunc();
  const [admins, setAdmins] = useState<TUser[]>([]);
  const [loading, setLoading] = useState(false);
  const { user } = Store.useUserStore();

  const fetchData = useCallback(async () => {
    if (loading) return;
    try {
      setLoading(true);
      const res = await Store.Api.User.list({ clientId: null });
      if (res.data.data && res.data.success) {
        setAdmins(res.data.data);
      }
    } catch (error) {
    } finally {
      setLoading(false);
    }
  }, [loading])

  useEffect(() => {
    if (user?.id) {
      fetchData()
    }
  }, [user?.id])

  const showModalCreateAdmin = useCallback(() => {
    modal.show(
      <AdminDetailModal
        title='Create Global Admin'
        data={undefined}
        refresh={fetchData}
        isGlobalAdmin={true}
      />
    )
  }, [])

  const tableRows = useMemo(() => {
    if (!admins) return [];
    const onPressDelete = (user) => {
      modalConfirm.show({
        title: 'Delete',
        message: 'Do you want to delete this global admin account?',
        onConfirm: async () => {
          await Store.Api.User.remove({
            id: user.id,
          });
          fetchData()
        }
      })
    }
    const onPressEdit = (user) => {
      modal.show(
        <AdminDetailModal
          title='Edit Global Admin'
          data={user}
          refresh={fetchData}
          isGlobalAdmin={true}
        />
      )
    }

    return admins.map(i => ({
      ...i,
      actions: <Row justifyContent='flex-end'>
        <Tooltip trigger="mouseenter" title={"Delete admin account"}>
          <TouchField paddingHorizontal={12} paddingVertical={8} onPress={() => onPressDelete(i)}>
            <FontAwesome5 name='trash' size={16} color={COLOR.RED} />
            {/* <Text color={COLOR.RED}>Delete</Text> */}
          </TouchField>
        </Tooltip>
        <Tooltip trigger="mouseenter" title={"Edit admin account"}>
          <TouchField paddingHorizontal={12} paddingVertical={8} onPress={() => onPressEdit(i)}>
            <FontAwesome5 name='pen' size={16} color={COLOR.MAIN} />
            {/* <Text color={COLOR.MAIN}>Edit</Text> */}
          </TouchField>
        </Tooltip>
      </Row>
    }))
  }, [admins, fetchData])

  return (
    <CMSLayout requireAuthen>
      <Col flex1 m2 p2 round1 bgWhite shadow>
        <Row alignItems={'flex-start'} justifyContent={'space-between'}>
          <Text h4 mb2 nativeID={'heading'}>Global Admins</Text>
          <Row>
            <TouchField
              marginLeft={8}
              width={36} height={36}
              borderRadius={18} middle
              onPress={showModalCreateAdmin}
              dataSet={{ element: 'add-pathfinder' }}
            >
              <FontAwesome5 name='plus-circle' size={24} color={COLOR.MAIN} />
            </TouchField>
          </Row>
        </Row>
        <Table
          columns={[
            { key: 'email', title: 'Email', flex: 2 },
            { key: 'firstName', title: 'First Name', flex: 1 },
            { key: 'lastName', title: 'Last Name', flex: 1 },
            { key: 'actions', title: '', width: 150 },
          ]}
          data={tableRows}
          style={{ margin: 20 }}
          minWidthRequired={500}
          isLoading={loading}
          rowProps={{
            paddingVertical: 16,
          }}
        />
      </Col>
    </CMSLayout>
  )
};

ListGlobalAdmin.routeInfo = {
  title: 'Pathfinder - List Global Admin',
  path: '/admins',
};

export default ListGlobalAdmin;
