import { Col, Text, Button, Input, useUIState } from 'components';
import { IScreen } from 'type';
import React, { useState } from 'react';
import { COLOR, SCREEN } from 'const';
import { SVG } from 'assets';
import Store from 'store';
import { useNavFunc } from 'navigation';

const Login: IScreen = () => {
  const { navigate, route } = useNavFunc();
  const { redirect }: any = route.params || {};
  const UserStore = Store.useUserStore();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [{ loading, errorMes }, setUI] = useUIState();

  const handleLogin = async () => {
    setUI({ loading: true });
    try {
      if (!email || !password) return alert('Please input your email and password');
      const res = await Store.Client.Api.User.login({
        email,
        password,
      });
      if (res.data.success && res.data.data?.token) {
        setUI({ loading: false, errorMes: '' });
        const token = res.data.data?.token;
        Store.Client.setToken(token);
        UserStore.set({
          token,
          user: res.data.data?.publicInfo,
        });
        if (redirect) {
          navigate(redirect);
        } else {
          navigate(SCREEN.Home);
        }
      } else {
        // return alert('Error: ' + res.data.error);
        setUI({ loading: false, errorMes: res.data.error });
      }
    } catch (err) {
      setUI({ loading: false, errorMes: String(err) });
    }
  };

  return (
    <Col flex1 middle backgroundColor={COLOR.GREY_BG}>

      <Col alignSelft='center' maxWidth={320} width={'50%'} bgWhite shadow round0 overflow={'hidden'}>
        <Col backgroundColor={COLOR.MAIN} height={120} middle>
          <SVG.BottleGoose
            fill='white'
            width={180}
          />
        </Col>
        <Col p2>
          {errorMes ? (
            <Text color="red" body1 bold>{errorMes}</Text>
          ) : (
            <Text>Welcome</Text>
          )}
          <Input
            mt2
            value={email}
            onChange={setEmail}
            placeholder={"Input your email"}
          />
          <Input
            mt2
            value={password}
            onChange={setPassword}
            placeholder={"Input your password"}
            password
          />
          <Button
            mt2
            solid
            text='LOGIN'
            width='100%'
            onPress={handleLogin}
            isLoading={loading}
          />

        </Col>
      </Col>

    </Col>
  )
}

Login.routeInfo = {
  title: 'Login',
  path: '/login',
}

export default Login;