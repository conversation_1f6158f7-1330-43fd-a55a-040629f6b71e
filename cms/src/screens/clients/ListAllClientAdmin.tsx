import { Col, Text, CMSLayout, Table, Row, TouchField, modal, modalConfirm, AdminDetailModal, ClientName } from 'components';
import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { IScreen, TUser } from 'type';
import Store from 'store';
import { useNavFunc } from 'navigation';
import { COLOR } from 'const';
import { FontAwesome5 } from '@expo/vector-icons';
import shallow from 'zustand/shallow';
import { Tooltip } from 'react-tippy';

const ListAllClientAdmin: IScreen = () => {
  const { navigate } = useNavFunc();
  const [admins, setAdmins] = useState<TUser[]>([]);
  const [loading, setLoading] = useState(false);
  const { getClients } = Store.useClientStore(state => ({
    getClients: state.getClients,
  }), shallow);

  const fetchData = useCallback(async () => {
    if (loading) return;
    try {
      setLoading(true);
      const res = await Store.Api.User.list({ allClient: true, clientId: null });
      if (res.data.data && res.data.success) {
        setAdmins(res.data.data);
      }
    } catch (error) {
    } finally {
      setLoading(false);
    }
  }, [loading])

  useEffect(() => {
    getClients();
    setTimeout(() => {
      fetchData();
    }, 100)
  }, [])

  const showModalCreateAdmin = useCallback(() => {
    modal.show(
      <AdminDetailModal
        title='Create Admin'
        data={undefined}
        refresh={fetchData}
        isAdmin
      />
    )
  }, [])

  const tableRows = useMemo(() => {
    if (!admins) return [];
    const onPressDelete = (user) => {
      modalConfirm.show({
        title: 'Delete',
        message: 'Do you want to delete this admin account?',
        onConfirm: async () => {
          await Store.Api.User.remove({
            id: user.id,
          });
          fetchData()
        }
      })
    }
    const onPressEdit = (user) => {
      modal.show(
        <AdminDetailModal
          title='Edit Admin'
          data={user}
          refresh={fetchData}
          isAdmin
        />
      )
    }

    return admins.map(i => ({
      ...i,
      client: <ClientName clientId={i.clientId} />,
      actions: <Row justifyContent='flex-end'>
        <Tooltip trigger="mouseenter" title={"Delete admin account"}>
          <TouchField paddingHorizontal={12} paddingVertical={8} onPress={() => onPressDelete(i)}>
            <FontAwesome5 name='trash' size={16} color={COLOR.RED} />
            {/* <Text color={COLOR.RED}>Delete</Text> */}
          </TouchField>
        </Tooltip>
        <Tooltip trigger="mouseenter" title={"Edit admin account"}>
          <TouchField paddingHorizontal={12} paddingVertical={8} onPress={() => onPressEdit(i)}>
            <FontAwesome5 name='pen' size={16} color={COLOR.MAIN} />
            {/* <Text color={COLOR.MAIN}>Edit</Text> */}
          </TouchField>
        </Tooltip>
      </Row>
    }))
  }, [admins, fetchData])

  return (
    <CMSLayout requireAuthen>
      <Col flex1 m2 p2 round1 bgWhite shadow>
        <Row alignItems={'flex-start'} justifyContent={'space-between'}>
          <Text h4 mb2 nativeID={'heading'}>Client Admins</Text>
          <Row>
            <TouchField
              marginLeft={8}
              width={36} height={36}
              borderRadius={18} middle
              onPress={showModalCreateAdmin}
              dataSet={{ element: 'add-pathfinder' }}
            >
              <FontAwesome5 name='plus-circle' size={24} color={COLOR.MAIN} />
            </TouchField>
          </Row>
        </Row>
        <Table
          columns={[
            { key: 'email', title: 'Email', flex: 1 },
            { key: 'firstName', title: 'First Name', flex: 1 },
            { key: 'lastName', title: 'Last Name', flex: 1 },
            { key: 'client', title: 'Client', flex: 1 },
            { key: 'actions', title: '', width: 150 },
          ]}
          data={tableRows}
          style={{ margin: 20 }}
          minWidthRequired={500}
          isLoading={loading}
          rowProps={{
            paddingVertical: 16,
          }}
        />
      </Col>
    </CMSLayout>
  )
};

ListAllClientAdmin.routeInfo = {
  title: 'Client Admins',
  path: '/client-admins',
};

export default ListAllClientAdmin;
