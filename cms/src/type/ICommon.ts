import { TLearning } from "store/api-client"

export interface ElementImportItem {
  filters?: string
  image?: string
  name?: string
  rolloverText?: string
}

export interface StatementImportItem {
  element?: string
  learning1?: string
  learning2?: string
  learning3?: string
  rollover1?: string
  rollover2?: string
  rollover3?: string
  statement?: string
}

export interface PathfinderImportItem {
  additionalInstruction?: string
  completionInstruction?: string
  welcomeInstruction?: string
  elements?: ElementImportItem[]
  elementsTitle?: string
  likertScale?: string // "Always,Sometimes,Never"
  logo?: string
  name?: string
  statements?: StatementImportItem[]
  learnings?: TLearning[]
}
