/// <reference types="cypress" />

describe('Logging into CMS as Global admin', () => {
  beforeEach(() => {
    cy.visit('http://localhost:19006');
  })

  it('redirects to login page', () => {
    cy.location('pathname').should('eq', '/login')
  })

  // it('login and see admin nav menu', () => {
  //   cy.get('#input-your-email')
  //     .type('<EMAIL>').should('have.value', '<EMAIL>');
  //   cy.get('#input-your-password')
  //     .type('CD%2023{enter}');
  //   cy.location({ timeout: 8000 }).should((loc) => {
  //     expect(loc.pathname).to.eq('/')
  //   });

  //   cy.get('#sidebar a').should('have.length', 3);
  //   cy.get('#sidebar a').first().should("have.attr", "href", "/admins").should(el => {
  //     expect(el.find('[data-element="nav-title"]').text()).to.eq('Admins')
  //   });
  //   cy.get('#sidebar a').eq(1).should("have.attr", "href", "/clients").should(el => {
  //     expect(el.find('[data-element="nav-title"]').text()).to.eq('Clients')
  //   });
  //   cy.get('#sidebar a').last().should("have.attr", "href", "/report/global").should(el => {
  //     expect(el.find('[data-element="nav-title"]').text()).to.eq('Reports')
  //   });
  // })

  it('quick login without welcome message', () => {
    cy.visit('http://localhost:19006/login?noWelcomeMessage=1');
    cy.get('#input-your-email')
      .type('<EMAIL>').should('have.value', '<EMAIL>');
    cy.get('#input-your-password')
      .type('CD%2023{enter}');
    cy.location('pathname').should('eq', '/');

    cy.get('#sidebar a').should('have.length', 6);
    cy.get('#sidebar a').eq(0).should("have.attr", "href", "/admins").should(el => {
      expect(el.find('[data-element="nav-title"]').text()).to.eq('Admins')
    });
    cy.get('#sidebar a').eq(1).should("have.attr", "href", "/clients").should(el => {
      expect(el.find('[data-element="nav-title"]').text()).to.eq('Clients')
    });
    cy.get('#sidebar a').last().should("have.attr", "href", "/").should(el => {
      expect(el.find('[data-element="nav-title"]').text()).to.eq('Training Videos')
    });
  });
})
