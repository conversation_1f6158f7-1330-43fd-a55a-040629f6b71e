/// <reference types="cypress" />

describe('a Global Admin', () => {
  beforeEach(() => {
    cy.visit('http://localhost:19006/login-link?email=<EMAIL>&pass=CD%252023');
    cy.location('pathname').should('eq', '/');
  })

  it('I can impersonate client', { scrollBehavior: false }, () => {
    cy.get('#sidebar a').should('have.length', 6);
    cy.wait(1000);
    cy.get('[id="select-client-btn"]').first().click();
    cy.get('[id="select-client-modal"]').should('be.visible').and('contain.text', 'Login as Client Admin');
    cy.get('[id="client-item"]').should('have.length.greaterThan', 0);
    cy.wait(1000);
    cy.get('[id="client-item"]').eq(1).click();
    cy.get('#sidebar a').should('have.length', 7);
    
    cy.get('#sidebar a').eq(4).click();
    cy.get('#heading').should('have.text', "Pathfinders");
    cy.wait(1000);
  });

})
