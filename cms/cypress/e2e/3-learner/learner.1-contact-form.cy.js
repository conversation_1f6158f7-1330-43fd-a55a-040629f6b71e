/// <reference types="cypress" />

describe('Logging into Pathfinder as <PERSON><PERSON>', () => {
  beforeEach(() => {
    cy.visit('http://localhost:19006/pathfinders/pathfinder0/aaa');
  })

  it('it should show contact form at first', () => {
    cy.get('[data-element="contact-form"]').should('be.visible')
    cy.get('[data-element="input-firstName"]').eq(0).type('John');
    cy.get('[data-element="input-lastName"]').eq(0).type('Doe');
    cy.get('[data-element="input-email"]').eq(0).type('<EMAIL>');
    cy.wait(500);
  });
})
