/// <reference types="cypress" />

import { checkPathfinderInput, typingToPathfinderInput } from "../../support/utils";

describe('doing Pathfinder flow as a Client Admin', () => {
  beforeEach(() => {
    cy.visit('http://localhost:19006/login-link?email=<EMAIL>&pass=CD%252023&redirect=ListPathfinder');
    cy.location('pathname').should('eq', '/pathfinders');
  })

  it('I can edit Email settings of Pathfinder', { scrollBehavior: false }, () => {
    cy.get('#heading').should('have.text', "Pathfinders");
    cy.wait(500);
    cy.get('[data-element="add-pathfinder"]').eq(0).click();
    cy.wait(500);
    cy.get('[data-element="pathfinder-layout"] [data-element="pathfinder-tab"]').eq(1).click();
    cy.wait(500);

    typingToPathfinderInput(0, 0, '<EMAIL>')
    typingToPathfinderInput(0, 1, 'Your Pathfinder Personal Learning Recommendations')

    cy.get('[data-element="pathfinder-layout"]').eq(0).find('[class="ql-editor ql-blank"]')
      .eq(0).click().type('Best regards', {
        force: true,
        waitForAnimations: false,
      });

    cy.get('[data-element="auto-saver"]').should('be.visible')
    cy.wait(3000);
    cy.reload(true);
    cy.wait(500);

    cy.get('[data-element="pathfinder-layout"] [data-element="pathfinder-tab"]').eq(1).click();
    cy.wait(500);

    checkPathfinderInput(0, 0, '<EMAIL>')
    checkPathfinderInput(0, 1, 'Your Pathfinder Personal Learning Recommendations')
    cy.get('[data-element="pathfinder-layout"]').eq(0).find('[class="ql-editor"]')
      .eq(1).should('have.text', 'Best regards');

    cy.wait(500);
  });
});
