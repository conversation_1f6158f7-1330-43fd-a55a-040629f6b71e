/// <reference types="cypress" />

import { checkPathfinderInput } from "../../support/utils";

describe('doing Pathfinder flow as a Client Admin', () => {
  beforeEach(() => {
    cy.visit('http://localhost:19006/login-link?email=<EMAIL>&pass=CD%252023&redirect=ListPathfinder');
    cy.location('pathname').should('eq', '/pathfinders');
  })

  it('view Pathfinders', { scrollBehavior: false }, () => {
    cy.get('#heading').should('have.text', "Pathfinders");
    cy.scrollTo('bottom');
    cy.wait(1000);
    cy.scrollTo('top');
    cy.wait(1000);

    cy.get('[data-element="pathfinder-layout"]').eq(1).find('[id="expand-pathfinder"]').first().click({ force: true })

    checkPathfinderInput(1, 0, 'Work 10 Updated')
    checkPathfinderInput(1, 1, 'Select your role to find course in less than 2 minutes! Updated')
    checkPathfinderInput(1, 2, 'Addition Instruction')
    checkPathfinderInput(1, 3, 'Congratulations {{.FirstName}} for completing your {{.PathfinderName}} Pathfinder')
    checkPathfinderInput(1, 4, 'Learning Persona')
    checkPathfinderInput(1, 5, 'Never')
    checkPathfinderInput(1, 6, 'Sometimes')
    checkPathfinderInput(1, 7, 'Always')

    cy.wait(1000);

    cy.get('[data-element="pathfinder-layout"] [data-element="pathfinder-tab"]').eq(1)
      .click().should('have.css', 'border-bottom-width', '2px');
    cy.wait(500);
    cy.get('[data-element="pathfinder-layout"] [data-element="pathfinder-tab"]').eq(2)
      .click().should('have.css', 'border-bottom-width', '2px');
    cy.wait(500);
    cy.get('[data-element="pathfinder-layout"] [data-element="pathfinder-tab"]').eq(3)
      .click().should('have.css', 'border-bottom-width', '2px');
    cy.wait(500);
  });
})
