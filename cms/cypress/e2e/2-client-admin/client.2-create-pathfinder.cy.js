/// <reference types="cypress" />

import { checkPathfinderInput, clearAndTypingToPathfinderInput, saveCurrentPathfinder, typingToPathfinderInput } from "../../support/utils";

describe('doing Pathfinder flow as a Client Admin', () => {
  beforeEach(() => {
    cy.visit('http://localhost:19006/login-link?email=<EMAIL>&pass=CD%252023');
    cy.location('pathname').should('eq', '/');
  })

  it('create multi Pathfinder', { scrollBehavior: false }, () => {
    cy.get('#sidebar a').eq(4).click();
    cy.wait(1000);
    cy.get('#heading').should('have.text', "Pathfinders");
    cy.get('[data-element="add-pathfinder"]').eq(0).click();
    cy.wait(500);

    clearAndTypingToPathfinderInput(0, 0, 'Work 10')
    clearAndTypingToPathfinderInput(0, 1, 'Select your role to find course in less than 2 minutes!')
    clearAndTypingToPathfinderInput(0, 2, 'Addition Instruction')
    clearAndTypingToPathfinderInput(0, 3, 'Congratulations {{}{{}.FirstName{}}{}} for completing your {{}{{}.PathfinderName{}}{}} Pathfinder')
    clearAndTypingToPathfinderInput(0, 4, 'Learning Persona')
    clearAndTypingToPathfinderInput(0, 5, 'Never')
    clearAndTypingToPathfinderInput(0, 6, 'Sometimes')
    clearAndTypingToPathfinderInput(0, 7, 'Always')

    cy.intercept('POST', '**/api/pathfinders/create').as('createPathfinder')

    cy.intercept('PUT', 'https://pathfinder-cd.fra1.digitaloceanspaces.com/files/*').as('uploadImage')

    cy.get('[data-element="pathfinder-layout"]').first().find('input[type=file]')
      .selectFile('cypress/fixtures/linklaters.png', { force: true })

    cy.wait('@uploadImage', { timeout: 20000 }).its('response.statusCode').should('eq', 200)

    cy.get('[data-element="pathfinder-layout"]').first().find('img', { timeout: 10000 })
      .should('have.attr', 'src')
      .and('include', 'pathfinder-cd.fra1.digitaloceanspaces.com/files')

    cy.wait('@createPathfinder', { timeout: 20000 }).then(({ response }) => {
      saveCurrentPathfinder(response.body.data);
      cy.wrap(response.body.data).as('currentPathfinder');
    })

    cy.wait(1000)
    cy.get('[data-element="add-pathfinder"]').eq(0).click();

    cy.wait(500);
    cy.scrollTo('top');
    cy.wait(1000);

    clearAndTypingToPathfinderInput(0, 0, 'Office 365')
    clearAndTypingToPathfinderInput(0, 1, 'Select your role to pinpoint your {{}{{}.PathfinderName{}}{}}')

    cy.get('[data-element="auto-saver"]').should('be.visible')
    cy.wait(5000);
    cy.reload(true);
    cy.wait(400);

    checkPathfinderInput(0, 0, 'Office 365')
    checkPathfinderInput(0, 1, 'Select your role to pinpoint your {{.PathfinderName}}')

    cy.get('[data-element="pathfinder-layout"]').eq(1).find('[id="expand-pathfinder"]').first().click({ force: true })

    checkPathfinderInput(1, 0, 'Work 10')
    checkPathfinderInput(1, 1, 'Select your role to find course in less than 2 minutes!')
    checkPathfinderInput(1, 2, 'Addition Instruction')
    checkPathfinderInput(1, 3, 'Congratulations {{.FirstName}} for completing your {{.PathfinderName}} Pathfinder')
    checkPathfinderInput(1, 4, 'Learning Persona')
    checkPathfinderInput(1, 5, 'Never')
    checkPathfinderInput(1, 6, 'Sometimes')
    checkPathfinderInput(1, 7, 'Always')

    cy.wait(1000);
  });

  it('update multi Pathfinder', { scrollBehavior: false }, () => {
    cy.get('#sidebar a').eq(4).click();
    cy.wait(1000);
    cy.get('#heading').should('have.text', "Pathfinders");
    cy.wait(500);

    typingToPathfinderInput(0, 0, ' Updated')
    typingToPathfinderInput(0, 1, ' Updated')
    cy.wait(4000);

    cy.get('[data-element="pathfinder-layout"]').eq(1).find('[id="expand-pathfinder"]').first().click({ force: true })

    typingToPathfinderInput(1, 0, ' Updated')
    typingToPathfinderInput(1, 1, ' Updated')

    cy.get('[data-element="auto-saver"]').should('be.visible')

    cy.wait(3000);
    cy.reload(true);
    cy.wait(400);

    checkPathfinderInput(0, 0, 'Office 365 Updated')
    checkPathfinderInput(0, 1, 'Select your role to pinpoint your {{.PathfinderName}} Updated')

    cy.get('[data-element="pathfinder-layout"]').eq(1).find('[id="expand-pathfinder"]').first().click({ force: true })

    checkPathfinderInput(1, 0, 'Work 10 Updated')
    checkPathfinderInput(1, 1, 'Select your role to find course in less than 2 minutes! Updated')
    checkPathfinderInput(1, 2, 'Addition Instruction')
    checkPathfinderInput(1, 3, 'Congratulations {{.FirstName}} for completing your {{.PathfinderName}} Pathfinder')
    checkPathfinderInput(1, 4, 'Learning Persona')
    checkPathfinderInput(1, 5, 'Never')
    checkPathfinderInput(1, 6, 'Sometimes')
    checkPathfinderInput(1, 7, 'Always')

    cy.wait(1000);
  });
})
