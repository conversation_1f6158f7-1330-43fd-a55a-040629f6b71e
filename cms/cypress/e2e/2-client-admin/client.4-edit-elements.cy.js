/// <reference types="cypress" />

import { checkPathfinderInput, clearAndTypingToPathfinderInput, typingToPathfinderInput } from "../../support/utils";

describe('doing Pathfinder flow as a Client Admin', () => {
  beforeEach(() => {
    cy.visit('http://localhost:19006/login-link?email=<EMAIL>&pass=CD%252023&redirect=ListPathfinder');
    cy.location('pathname').should('eq', '/pathfinders');
  })

  it('I can add Elements of Pathfinder', { scrollBehavior: false }, () => {
    cy.get('#heading').should('have.text', "Pathfinders");
    cy.wait(1000);

    checkPathfinderInput(0, 0, 'Office 365 Updated')
    checkPathfinderInput(0, 1, 'Select your role to pinpoint your {{.PathfinderName}} Updated')

    cy.get('[data-element="pathfinder-layout"] [data-element="pathfinder-tab"]').eq(2).click();
    cy.wait(500);

    cy.get('[data-element="add-pathfinder-entity"]').eq(0).click();
    cy.wait(500);

    clearAndTypingToPathfinderInput(0, 0, 'Author')
    clearAndTypingToPathfinderInput(0, 1, 'Create documents fron scratch; researches')
    cy.get('[data-element="select-filter-keys"]').eq(0).find('input').eq(0)
      .type('New York,Litigation,Corporate,');

    cy.intercept('PUT', 'https://pathfinder-cd.fra1.digitaloceanspaces.com/files/*').as('uploadImage')

    cy.get('[data-element="pathfinder-layout"]').first().find('input[type=file]').eq(1)
      .selectFile('cypress/fixtures/author.png', { force: true })

    cy.wait('@uploadImage', { timeout: 20000 }).its('response.statusCode').should('eq', 200)

    cy.get('[data-element="pathfinder-layout"]').first().find('img', { timeout: 10000 })
      .should('have.attr', 'src')
      .and('include', 'pathfinder-cd.fra1.digitaloceanspaces.com/files')

    cy.wait(500);
    cy.get('[data-element="auto-saver"]').should('be.visible');
    cy.wait(4000);

    cy.get('[data-element="add-pathfinder-entity"]').eq(0).click();

    cy.get('[data-element="select-filter-keys"]').eq(1).find('input').eq(0).click({ force: true })
    cy.get('div[id$="-listbox"]').eq(0).find('div[id$="option-0"]').first()
      .should('have.text', 'New York');
    cy.get('div[id$="-listbox"]').eq(0).find('div[id$="option-1"]').first()
      .should('have.text', 'Litigation');
    cy.get('div[id$="-listbox"]').eq(0).find('div[id$="option-2"]').first()
      .should('have.text', 'Corporate');
    cy.wait(1000);

    cy.reload(true);
    cy.wait(500);

    checkPathfinderInput(0, 0, 'Office 365 Updated')

    cy.get('[data-element="pathfinder-layout"] [data-element="pathfinder-tab"]').eq(2).click();
    cy.wait(500);

    checkPathfinderInput(0, 0, 'Author')
    checkPathfinderInput(0, 1, 'Create documents fron scratch; researches')
    cy.get('[data-element="select-filter-keys"]').eq(0).find('div[class$="multiValue"]')
      .should('have.length', 4); // include "+ add new" placeholder tag
    cy.get('[data-element="select-filter-keys"]').eq(0).find('div[class$="multiValue"]').eq(0)
      .should('have.text', 'New York');
    cy.get('[data-element="select-filter-keys"]').eq(0).find('div[class$="multiValue"]').eq(1)
      .should('have.text', 'Litigation');
    cy.get('[data-element="select-filter-keys"]').eq(0).find('div[class$="multiValue"]').eq(2)
      .should('have.text', 'Corporate');
    cy.wait(1000);
  });

  it('I can update Elements of Pathfinder', { scrollBehavior: false }, () => {
    cy.get('#heading').should('have.text', "Pathfinders");
    cy.wait(1000);

    cy.get('[data-element="pathfinder-layout"] [data-element="pathfinder-tab"]').eq(2).click();
    cy.wait(500);

    typingToPathfinderInput(0, 0, '{selectall}{backspace}Reviewer')
    typingToPathfinderInput(0, 1, '{selectall}{backspace}Does not create documents; reviews, make comments')

    cy.get('[data-element="select-filter-keys"]').eq(0).find('input').eq(0)
      .type('San Francisco,');

    cy.get('[data-element="auto-saver"]').should('be.visible')
    cy.wait(5000);
    cy.reload(true);
    cy.wait(500);

    cy.get('[data-element="pathfinder-layout"] [data-element="pathfinder-tab"]').eq(2).click();
    cy.wait(500);

    checkPathfinderInput(0, 0, 'Reviewer')
    checkPathfinderInput(0, 1, 'Does not create documents; reviews, make comments')
    cy.get('[data-element="select-filter-keys"]').eq(0).find('div[class$="multiValue"]')
      .should('have.length', 5);  // include "+ add new" placeholder tag
    cy.get('[data-element="select-filter-keys"]').eq(0).find('div[class$="multiValue"]').eq(0)
      .should('have.text', 'New York');
    cy.get('[data-element="select-filter-keys"]').eq(0).find('div[class$="multiValue"]').eq(1)
      .should('have.text', 'Litigation');
    cy.get('[data-element="select-filter-keys"]').eq(0).find('div[class$="multiValue"]').eq(2)
      .should('have.text', 'Corporate');
    cy.get('[data-element="select-filter-keys"]').eq(0).find('div[class$="multiValue"]').eq(3)
      .should('have.text', 'San Francisco');
    cy.wait(1000);
  });
})
