/// <reference types="cypress" />

describe('Logging into CMS as Global admin', () => {
  beforeEach(() => {
    cy.visit('http://localhost:19006');
  })

  it('redirects to login page', () => {
    cy.location('pathname').should('eq', '/login')
  })

  it('quick login without welcome message', () => {
    cy.visit('http://localhost:19006/login?noWelcomeMessage=1');
    cy.get('#input-your-email')
      .type('<EMAIL>').should('have.value', '<EMAIL>');
    cy.get('#input-your-password')
      .type('CD%2023{enter}');
    cy.location('pathname').should('eq', '/');

    cy.get('#sidebar a').should('have.length', 7);
    cy.get('#sidebar a').eq(0).should("have.attr", "href", "/client-admin").should(el => {
      expect(el.find('[data-element="nav-title"]').text()).to.eq('Client Admins')
    });
    cy.get('#sidebar a').eq(1).should("have.attr", "href", "/my-info").should(el => {
      expect(el.find('[data-element="nav-title"]').text()).to.eq('Company Logo')
    });
    cy.get('#sidebar a').eq(2).should("have.attr", "href", "/manage-tags").should(el => {
      expect(el.find('[data-element="nav-title"]').text()).to.eq('Manage Tags')
    });
    cy.get('#sidebar a').eq(3).should("have.attr", "href", "/manage-learnings").should(el => {
      expect(el.find('[data-element="nav-title"]').text()).to.eq('Manage Learnings')
    });
    cy.get('#sidebar a').eq(4).should("have.attr", "href", "/pathfinders").should(el => {
      expect(el.find('[data-element="nav-title"]').text()).to.eq('Pathfinders')
    });
    cy.get('#sidebar a').eq(5).should("have.attr", "href", "/my-report").should(el => {
      expect(el.find('[data-element="nav-title"]').text()).to.eq('Reports')
    });
  });
})
