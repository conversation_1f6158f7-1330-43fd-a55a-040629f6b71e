/// <reference types="cypress" />

import { checkPathfinderInput, clearAndTypingToPathfinderInput, typingToPathfinderInput } from "../../support/utils";

describe('doing Pathfinder flow as a Client Admin', () => {
  beforeEach(() => {
    cy.visit('http://localhost:19006/login-link?email=<EMAIL>&pass=CD%252023&redirect=ListPathfinder');
    cy.location('pathname').should('eq', '/pathfinders');
  })

  it('I can add Statements of Pathfinder', { scrollBehavior: false }, () => {
    cy.get('#heading').should('have.text', "Pathfinders");
    cy.wait(500);

    cy.get('[data-element="pathfinder-layout"] [data-element="pathfinder-tab"]').eq(3).click();
    cy.wait(500);

    cy.get('[data-element="add-pathfinder-entity"]').eq(0).click();
    cy.wait(500);

    clearAndTypingToPathfinderInput(0, 0, 'I use Office 365 at home.')
    clearAndTypingToPathfinderInput(0, 1, 'I have 365 at home and use it all the time.')
    clearAndTypingToPathfinderInput(0, 2, 'I have 365 at home and am familiar with some of the new features.')
    clearAndTypingToPathfinderInput(0, 3, 'I have never seen or used Office 365.')

    cy.get('[data-element="select-learnings"]').eq(0).click({ force: true })
    cy.wait(500);
    cy.get('div[id="learning-item-0"]').first().click()
    cy.get('div[id="learning-item-1"]').first().click()
    cy.get('[id="btn-confirm-modal"]').eq(0).click({ force: true })

    cy.get('[data-element="select-learnings"]').eq(1).click({ force: true })
    cy.wait(500);
    cy.get('div[id="learning-item-0"]').first().click()
    cy.get('[id="btn-confirm-modal"]').eq(0).click({ force: true })

    cy.get('[data-element="select-learnings"]').eq(2).click({ force: true })
    cy.wait(500);
    cy.get('div[id="learning-item-1"]').first().click()
    cy.get('[id="btn-confirm-modal"]').eq(0).click({ force: true })

    cy.get('[data-element="select-elements"]').eq(0).find('input').eq(0).click({ force: true })
    cy.get('div[id$="-listbox"]').eq(0).find('div[id$="option-1"]').first().click()

    cy.get('[data-element="auto-saver"]').should('be.visible')
    cy.wait(3000);
    cy.reload(true);
    cy.wait(500);

    cy.get('[data-element="pathfinder-layout"] [data-element="pathfinder-tab"]').eq(3).click();
    cy.wait(500);

    checkPathfinderInput(0, 0, 'I use Office 365 at home.')
    checkPathfinderInput(0, 1, 'I have 365 at home and use it all the time.')
    checkPathfinderInput(0, 2, 'I have 365 at home and am familiar with some of the new features.')
    checkPathfinderInput(0, 3, 'I have never seen or used Office 365.')

    cy.get('[data-element="select-learnings"]').eq(0).find('div[class$="multiValue"]')
      .should('have.length', 2);
    cy.get('[data-element="select-learnings"]').eq(1).find('div[class$="multiValue"]')
      .should('have.length', 1);
    cy.get('[data-element="select-learnings"]').eq(2).find('div[class$="multiValue"]')
      .should('have.length', 1);
    cy.get('[data-element="select-elements"]').eq(0).find('div[class$="multiValue"]')
      .should('have.length', 1);

    cy.wait(500);
  });

  it('I can edit Statement of Pathfinder', { scrollBehavior: false }, () => {
    cy.get('#heading').should('have.text', "Pathfinders");
    cy.wait(500);

    cy.get('[data-element="pathfinder-layout"] [data-element="pathfinder-tab"]').eq(3).click();
    cy.wait(500);

    checkPathfinderInput(0, 0, 'I use Office 365 at home.')
    checkPathfinderInput(0, 1, 'I have 365 at home and use it all the time.')

    cy.wait(500);

    typingToPathfinderInput(0, 0, '{backspace};')
    typingToPathfinderInput(0, 1, '{backspace};')

    cy.get('[data-element="auto-saver"]').should('be.visible')
    cy.wait(3000);
    cy.reload(true);
    cy.wait(500);

    cy.get('[data-element="pathfinder-layout"] [data-element="pathfinder-tab"]').eq(3).click();
    cy.wait(500);

    checkPathfinderInput(0, 0, 'I use Office 365 at home;')
    checkPathfinderInput(0, 1, 'I have 365 at home and use it all the time;')
    checkPathfinderInput(0, 2, 'I have 365 at home and am familiar with some of the new features.')
    checkPathfinderInput(0, 3, 'I have never seen or used Office 365.')

    cy.wait(500);
  });
})
