{"name": "cms", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start --web", "web-build": "npx expo export:web", "build": "rm -rf build && yarn web-build && mv web-build build", "sync-api-local": "rm -rf ./src/store/api-client && cp -r ../api-client ./src/store", "nginx-deploy": "yarn build && rm -rf /var/www/cms && cp -r build /var/www/cms", "test": "npx cypress open", "postinstall": "patch-package", "postuninstall": "patch-package"}, "dependencies": {"@expo/vector-icons": "^13.0.0", "@expo/webpack-config": "^0.17.0", "@react-native-community/masked-view": "0.1.10", "@react-navigation/native": "^5.9.4", "@react-navigation/stack": "^5.14.5", "@react-navigation/tabs": "^0.0.0-alpha.12", "@stripe/react-stripe-js": "^1.16.1", "@stripe/stripe-js": "^1.46.0", "expo": "~46.0.8", "expo-firebase-recaptcha": "^2.3.1", "expo-status-bar": "~1.4.0", "file-saver": "^2.0.5", "firebase": "^10.8.1", "firebase-admin": "^12.0.0", "jszip": "^3.10.1", "lighten-darken-color": "^1.0.0", "lodash": "^4.17.21", "moment": "^2.29.4", "otp-input-react": "^0.3.0", "quickly-react": "0.1.2", "react": "18.0.0", "react-beautiful-dnd": "^13.1.1", "react-datepicker": "^4.16.0", "react-dom": "18.0.0", "react-html-parser": "^2.0.2", "react-native": "0.69.4", "react-native-animatable": "^1.3.3", "react-native-gesture-handler": "~2.5.0", "react-native-reanimated": "~2.9.1", "react-native-safe-area-context": "4.3.1", "react-native-screens": "~3.15.0", "react-native-svg": "12.3.0", "react-native-web": "~0.18.7", "react-native-webview": "^13.8.1", "react-phone-input-2": "^2.15.1", "react-quill": "^2.0.0", "react-select": "^5.4.0", "react-tippy": "^1.4.0", "route-parser": "^0.0.5", "scheduler": "^0.23.0", "uuidv4": "^6.2.13", "xlsx": "0.18.5", "zustand": "^4.1.0"}, "devDependencies": {"@babel/core": "^7.12.9", "@types/lodash": "^4.14.195", "@types/node": "^18.7.6", "@types/react": "^18.0.17", "@types/react-native": "^0.69.5", "babel-plugin-module-resolver": "^4.1.0", "cypress": "^12.15.0", "cypress-fail-fast": "^7.0.1", "patch-package": "^8.0.0", "typescript": "^4.7.4"}, "private": true}