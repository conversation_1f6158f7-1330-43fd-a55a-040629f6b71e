module.exports = {
  apps: [
    {
      name: "backend",
      script: "./node_modules/.bin/ts-node ./index.ts",
      env: {
        NODE_PATH: "./src",
        DB_CONNECTION_STRING_DEV: '**************************************************************/pathfinder',
        POSTGRES_USER: 'pathfinder',
        POSTGRES_PASSWORD: 'pathfinder23123m12jk3bn1',
        POSTGRES_HOST: 'postgres',
        POSTGRES_PORT: 5432,
        SEED_ADMIN: '<EMAIL>',
        SEED_PASSWORD: 'CD%2023',
        AWS_ACCESS_KEY_ID: '********************',
        AWS_SECRET_ACCESS_KEY: '2DomG+89OSERel23GgZVhS7ompNYsF4IL3ocSRuX',
        AWS_S3_BUCKET: 'pathfinder-rebuild',
        AWS_REGION: 'eu-west-1',
        DO_ACCESS_KEY_ID: 'DO00T6DR7N39QD6YHW89',
        DO_SECRET_ACCESS_KEY: '4tcorkYc3hMgbpYF78IKWy08C1Uea1WPh0HzYeoZQes',
        DO_S3_BUCKET: 'pathfinder-cd',
        DO_REGION: 'fra1',
        PORT: 3344,
        FLUSH_ON_START: true,
        PROJECT_ID: "pathfinder-b5405",
        PRIVATE_KEY_ID: "ea03f6cdc4e79a55d13299d006c4d0ec2e15840e",
****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
        CLIENT_EMAIL: "<EMAIL>",
        CLIENT_ID: "104137171085824151278",
        AUTH_URI: "https://accounts.google.com/o/oauth2/auth",
        TOKEN_URI: "https://oauth2.googleapis.com/token",
        AUTH_PROVIDER_X509_CERT_URL: "https://www.googleapis.com/oauth2/v1/certs",
        CLIENT_X509_CERT_URL: "https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-q3llm%40pathfinder-b5405.iam.gserviceaccount.com",
        UNIVERSE_DOMAIN: "googleapis.com",
        REGION: "us",
        IS_MAIN_SERVER: 1,
        SUB_REGION_ENDPOINT: "http://localhost:3345/",
        MAIN_REGION_ENDPOINT: "http://localhost:3344/",
        FRONT_END_PF_DOMAIN: "http://localhost:19007/{{.slug}}/{{.region}}/{{.pathfinderId}}",
      },
    },
  ],
};
