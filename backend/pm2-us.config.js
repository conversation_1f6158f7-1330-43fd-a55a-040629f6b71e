module.exports = {
  apps: [
    {
      name: 'backend',
      script: './node_modules/.bin/ts-node ./index.ts',
      env: {
        NODE_PATH: './src',
        DB_CONNECTION_STRING_DEV: 'postgres://postgres:<EMAIL>:8032/pathfinder',
        POSTGRES_USER: 'pathfinder',
        POSTGRES_PASSWORD: 'pathfinder23123m12jk3bn1',
        POSTGRES_HOST: '127.0.0.1',
        POSTGRES_PORT: 5442,
        SEED_ADMIN: '<EMAIL>',
        SEED_PASSWORD: 'CD%2023',
        AWS_ACCESS_KEY_ID: '********************',
        AWS_SECRET_ACCESS_KEY: '2DomG+89OSERel23GgZVhS7ompNYsF4IL3ocSRuX',
        AWS_S3_BUCKET: 'pathfinder-rebuild',
        AWS_REGION: 'eu-west-1',
        DO_ACCESS_KEY_ID: 'DO00T6DR7N39QD6YHW89',
        DO_SECRET_ACCESS_KEY: '4tcorkYc3hMgbpYF78IKWy08C1Uea1WPh0HzYeoZQes',
        DO_S3_BUCKET: 'pathfinder-cd',
        DO_REGION: 'fra1',
        PORT: 3344,
        SMTP_HOST: 'email-smtp.eu-west-1.amazonaws.com',
        SMTP_USER: '********************',
        SMTP_PASSWORD: 'BItVDVkDU+IW3mmWPRN9zKWjOCCLV03S7ahC3mPxPY9J',
        SMTP_PORT: 465,
        SENDER_EMAIL: '@harborpathfinder.com',
        WEB_HOST: 'harborpathfinder.com',
        REGION: 'us',
        IS_MAIN_SERVER: 1,
        SUB_REGION_ENDPOINT: 'https://uk.harborpathfinder.com/',
        MAIN_REGION_ENDPOINT: 'https://us.harborpathfinder.com/',
        FRONT_END_PF_DOMAIN: 'https://{{.slug}}.learner.harborpathfinder.com/{{.region}}/{{.pathfinderId}}',
      },
    },
  ],
};
