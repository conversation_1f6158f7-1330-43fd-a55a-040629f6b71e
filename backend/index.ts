import "dotenv/config";
require('cross-fetch');
import Server from "./server";
const port = +process.env.PORT || 3000;
const server = new Server(port, {
  isMainServer: Boolean(process.env.IS_MAIN_SERVER),
  region: process.env.REGION,
  subRegionEndpoint: process.env.SUB_REGION_ENDPOINT,
  mainRegionEndpoint: process.env.MAIN_REGION_ENDPOINT,
  frontendPathfinderDomain: process.env.FRONT_END_PF_DOMAIN,
});

server.setupDatabase();

import { apiArrays } from './apiArrays';

server.initAPIs(apiArrays);

import { startCron } from './src/cron';
server.onDBReady().then(() => {
  console.log("DB ready");
  startCron();
});

import { genAPIClients } from './genAPIClient';
if (process.env.GENERATE_API_CLIENT) {
  genAPIClients(apiArrays);
}
