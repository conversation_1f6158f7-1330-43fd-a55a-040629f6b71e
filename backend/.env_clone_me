
GENERATE_API_CLIENT=0
DEV=1
DB_CONNECTION_STRING_DEV=postgresql://pathfinder:pathfinder23123m12jk3bn1@127.0.0.1:5442/pathfinder
SEED_ADMIN=<EMAIL>
SEED_PASSWORD=CD%2023
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=2DomG+89OSERel23GgZVhS7ompNYsF4IL3ocSRuX
AWS_S3_BUCKET=pathfinder-rebuild
AWS_REGION=eu-west-1
DO_ACCESS_KEY_ID=DO00T6DR7N39QD6YHW89
DO_SECRET_ACCESS_KEY=4tcorkYc3hMgbpYF78IKWy08C1Uea1WPh0HzYeoZQes
DO_S3_BUCKET=pathfinder-cd
DO_REGION=fra1
PORT=3344
POSTGRES_PASSWORD=pathfinder23123m12jk3bn1
POSTGRES_DB=postgres
POSTGRES_USER=pathfinder
POSTGRES_HOST=localhost
POSTGRES_PORT=5442
SMTP_HOST=xxxxx
SMTP_USER=xxxxx
SMTP_PASSWORD=xxxxx
SMTP_PORT=465
SENDER_EMAIL=@harborpathfinder.com
WEB_HOST=harborpathfinder.com
REGION=us
IS_MAIN_SERVER=1
SUB_REGION_ENDPOINT=xxxxxx
MAIN_REGION_ENDPOINT=xxxxxx
FRONT_END_PF_DOMAIN=http://localhost:19007/{{.slug}}/{{.region}}/{{.pathfinderId}}
