import qs = require('qs');
import { DB } from './src/db';
const fastify = require("fastify");

var admin = require("firebase-admin");

if (process.env.PROJECT_ID) {
  admin.initializeApp({
    credential: admin.credential.cert({
      "type": "service_account",
      "project_id": process.env.PROJECT_ID,
      "private_key_id": process.env.PRIVATE_KEY_ID,
      "private_key": process.env.PRIVATE_KEY,
      "client_email": process.env.CLIENT_EMAIL,
      "client_id": process.env.CLIENT_ID,
      "auth_uri": process.env.AUTH_URI,
      "token_uri": process.env.TOKEN_URI,
      "auth_provider_x509_cert_url": process.env.AUTH_PROVIDER_X509_CERT_URL,
      "client_x509_cert_url": process.env.CLIENT_X509_CERT_URL,
      "universe_domain": process.env.UNIVERSE_DOMAIN
    })
  });
}

const path = require('path')

type TBackendOptions = {
  isMainServer?: boolean;
  region?: string;
  subRegionEndpoint?: string;
  mainRegionEndpoint?: string;
  frontendPathfinderDomain?: string;
};
class Backend {
  constructor(port: number, opts : TBackendOptions | null = null) {
    this.instance = fastify({
      logger: true,
      disableRequestLogging: true,
      bodyLimit: ********, // ~ 30 mb
    });
    this.setupBasicMiddlewares();
    this.instance
      .listen(port, "0.0.0.0")
      .then(() => {
        console.log("🚀 THE BACKEND SERVER IS RUNNING AT PORT " + port);
      })
      .catch((err) => {
        console.log(err);
      });

    if (opts) {
      global.isMainServer = opts.isMainServer;
      global.region = opts.region;
      global.subRegionEndpoint = opts.subRegionEndpoint;
      global.mainRegionEndpoint = opts.mainRegionEndpoint;
      global.frontendPathfinderDomain = opts.frontendPathfinderDomain;
    }
  }

  instance;
  db;

  _countFeedApiCall = 0;

  // the middlewares that almost every express projects need
  setupBasicMiddlewares() {
    if (!this.instance) return;
    // this.instance.register(require('@fastify/formbody'))
    // this.instance.register(require('fastify-multipart'));
    // this.instance.addContentTypeParser('application/x-www-form-urlencoded', async function(request, payload, done) {
    //   console.log('PAYLOAD', payload);
    //   const parsed = qs.parse(payload);
    //   try {
    //     return parsed
    //   } catch(err) {
    //     return {};
    //   }
    // })
    this.instance.register(require('@fastify/static'), {
      root: path.join(__dirname, './src/uploads'),
      prefix: '/b/public/',
    });
    this.instance.addContentTypeParser(
      "application/x-www-form-urlencoded",
      { parseAs: "string" },
      function (req, body, done) {
        try {
          // console.log('body', body);
          done(null, qs.parse(body));
        } catch (error) {
          error.statusCode = 400;
          done(error, undefined);
        }
      }
    );
    this.instance.register(require('@fastify/multipart'), { attachFieldsToBody: true });
    this.instance.register(require("fastify-cors"), {
      // put your options here
      methods: "GET,PUT,POST, OPTIONS, DELETE",
      optionsSuccessStatus: 200,
    });

    this.instance.setErrorHandler(function (error, request, reply) {
      // Send error response
      reply.status(200).send({
        success: false,
        error: String(error),
        errorDetails: error,
      });
    });
  }

  // define apis
  async initAPIs(Apis: Array<any>) {
    if (!this.instance) return;
    if (!Array.isArray(Apis)) return;

    for (let i = 0; i < Apis.length; i++) {
      const ApiObject = Apis[i].apis;
      for (let api in ApiObject) {
        // console.log('api', api)
        const apiInstance = ApiObject[api];
        this.instance.route(apiInstance);
        // let apiInstance2 = apiInstance;
        // apiInstance2.url = `/:client_slug/:region${apiInstance.url}`;
        // this.instance.route(apiInstance2);
      }
    }
  }

  setupDatabase() {
    this.db = DB.init();
  }
  setupTestDatabase() {
    this.db = DB.initTest();
  }
  removeTestDatabase() {
    return DB.removeTest();
  }
  onDBReady = DB.onReady
}

export default Backend;
