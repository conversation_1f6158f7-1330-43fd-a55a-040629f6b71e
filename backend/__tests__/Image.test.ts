import "dotenv/config";
require('cross-fetch');
import Server from "../server";
import apiClient from '../../api-client';
import { apiArrays } from '../apiArrays';
import { LocalStorage } from "node-localstorage";
const port = Math.floor(Math.random() * 1000 + 2000);
const fs = require('fs');
const server = new Server(port);

let testClient;
beforeAll(async () => {
  server.setupTestDatabase();
  server.initAPIs(apiArrays);
  await server.onDBReady();
  apiClient.setHost(`http://localhost:${port}`);
  global.localStorage = new LocalStorage('./node-localstorage-data');
  await apiClient.Api.User.login({
    email: '<EMAIL>',
    password: 'CD%2023'
  });
  const res = await apiClient.Api.Client.create({
    name: 'ClientTest',
    logo: 'logo',
    location: 'location',
    slug: 'clienttest',
    region: 'uk',
  });
  testClient = res.data.data;
})

describe('As Client Admin', () => {
  let testClientAdmin;
  test('Login client admin account', async () => {
    const res = await apiClient.Api.User.createAccount({
      email: '<EMAIL>',
      password: '123456',
      firstName: 'tester',
      role: 'client',
      clientId: testClient.id,
    });
    testClientAdmin = res.data.data;
    await apiClient.Api.User.login({
      email: testClientAdmin.email,
      password: '123456',
    })
  });

  test('I can add image to library', async () => {
    const res = await apiClient.Api.Image.create({
      url: "https://google.com",
      name: "Name",
      type: "Type",
    });
    expect(res.data.success).toBe(true);
    expect(res.data.data.name).toBe("Name");
    expect(res.data.data.type).toBe("Type");
    expect(res.data.data.url).toBe("https://google.com");
  });

  test('I can view images in library', async () => {
    const res = await apiClient.Api.Image.list({});
    expect(res.data.success).toBe(true);
    expect(res.data.data.length).toBe(1);
    expect(res.data.data[0].name).toBe("Name");
    expect(res.data.data[0].url).toBe("https://google.com");
  });

});

afterAll(async () => {
  await server.removeTestDatabase();
})