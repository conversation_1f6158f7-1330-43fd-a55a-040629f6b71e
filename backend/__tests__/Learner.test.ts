import "dotenv/config";
require('cross-fetch');
import Server from "../server";
import apiClient from '../../api-client';
import { apiArrays } from '../apiArrays';
import { LocalStorage } from "node-localstorage";
const port = Math.floor(Math.random() * 1000 + 2000);
const fs = require('fs');
const server = new Server(port);

let testPathfinder;
let testClient;
let testElement;
let testStatements;
beforeAll(async () => {
  server.setupTestDatabase();
  server.initAPIs(apiArrays);
  await server.onDBReady();
  apiClient.setHost(`http://localhost:${port}`);
  global.localStorage = new LocalStorage('./node-localstorage-data');
  await apiClient.Api.User.login({
    email: '<EMAIL>',
    password: 'CD%2023'
  });
  const res = await apiClient.Api.Client.create({
    name: 'ClientTest',
    logo: 'logo',
    location: 'location',
    slug: 'clienttest',
    region: 'uk',
  });
  testClient = res.data.data;
  const res2 = await apiClient.Api.Pathfinder.create({
    name: 'Frontend dev',
    clientId: res.data.data.id,
  });
  testPathfinder = res2.data.data;
  const elms = [
    { idx: 1, keys: 'a,b,c' },
    { idx: 2, keys: 'c,a' },
  ]
  const res3 = await Promise.all(elms.map(async ({ idx, keys }) => {
    return await apiClient.Api.Element.create({
      pathfinderId: testPathfinder.id,
      clientId: testClient.id,
      name: `Element${idx}`,
      orderIndex: idx,
      filterKeys: keys,
    });
  }));
  testElement = res3[0].data.data;
  const res4 = await Promise.all(elms.map(async ({ idx }) => {
    return await apiClient.Api.Statement.create({
      clientId: testClient.id,
      elementIds: testElement.id,
      pathfinderId: testPathfinder.id,
      statement: `demo statement ${idx}`,
      orderIndex: idx,
    });
  }));
  testStatements = res4.map(i => i.data.data);
  await apiClient.Api.User.createAccount({
    email: '<EMAIL>',
    password: '123456',
    firstName: 'tester',
    lastName: '',
    role: 'client',
    clientId: testClient.id,
  });
})

describe('In specific pathfinder, as a Learner', () => {

  let testLearner;
  test('I can login after input my contact', async () => {
    const res = await apiClient.Api.Learner.contact({
      pathfinderId: testPathfinder.id,
      email: '<EMAIL>',
      firstName: 'tester',
    });
    testLearner = res.data.data;
    expect(res.data.success).toBe(true);
    expect(res.data.data.token).toBeDefined();
  });

  test('I can submit my likerts', async () => {
    const res = await apiClient.Api.Learner.submit({
      pathfinderId: testPathfinder.id,
      elementId: testElement.id,
      likerts: {
        [testStatements[0].id]: 1,
        [testStatements[1].id]: 2,
      }
    });
    expect(res.data.success).toBe(true);
    expect(Object.keys(res.data.data.likerts).length).toBe(2);
  });

});

describe('As client admin,', () => {

  test('I can see the report', async () => {
    await apiClient.Api.User.login({
      email: '<EMAIL>',
      password: '123456',
    });
    const res = await apiClient.Api.Learner.report({});
    expect(res.data.success).toBe(true);
    expect(res.data.data[0].pathfinderId).toBe(testPathfinder.id);
    expect(res.data.data[0].pathfinderName).toBe(testPathfinder.name);
    expect(Object.keys(res.data.data[0].likerts).length).toBe(2);
    const likert = res.data.data[0].likerts[testStatements[0].id];
    expect(likert.learnings).toBeDefined();
    expect(likert.likert).toBeDefined();
    expect(likert.statement).toBe(testStatements[0].statement);
  });

});

afterAll(async () => {
  await server.removeTestDatabase();
})
