import "dotenv/config";
require('cross-fetch');
import Server from "../server";
import { ApiClient } from '../../api-client';
import { apiArrays } from '../apiArrays';
import DB from '../src/db/DB.Postgres';
import { LocalStorage } from "node-localstorage";
import path = require("path");
const { spawn } = require('child_process');
var kill = require('tree-kill');
const port = Math.floor(Math.random() * 1000 + 2000);
const subPort = port + 1;
const fs = require('fs');
const subRegionEndpoint = `http://localhost:${subPort}`;
const mainRegionEndpoint = `http://localhost:${port}`;

const mainServerConfig = {
  isMainServer: true,
  region: "us",
  subRegionEndpoint,
  mainRegionEndpoint,
  frontendPathfinderDomain: `http://localhost:19007/{{.slug}}/{{.region}}/{{.pathfinderId}}`,
};

const server = new Server(port, mainServerConfig);
const mainApiClient = new ApiClient({ debugMode: false, singletonRequest: false });
const subApiClient = new ApiClient({ debugMode: false, singletonRequest: false });
let subServerProcess;
let subDBData;
let isTestDone = false;

beforeAll(async () => {
  server.initAPIs(apiArrays);
  global.localStorage = new LocalStorage('./node-localstorage-data');
  subDBData = await DB.createTestDBStandalone();

  // subServer.setupTestDatabase();
  // subServer.initAPIs(apiArrays);
  // await subServer.onDBReady();
  await new Promise(resolve => {
    process.env.NODE_PATH = "./src";
    subServerProcess = spawn('yarn', ['dev'], {
      cwd: path.join(__dirname, '../src'),
      env: {
        ...process.env,
        PORT: subPort,
        REGION: 'uk',
        MAIN_REGION_ENDPOINT: mainRegionEndpoint,
        SUB_REGION_ENDPOINT: subRegionEndpoint,
        FRONT_END_PF_DOMAIN: `http://localhost:19007/{{.slug}}/{{.region}}/{{.pathfinderId}}`,
        DB_CONNECTION_STRING_DEV: subDBData.connectionString,
        DEV: '1',
      }
    });
    // console.log('subServerProcess', subServerProcess);

    subServerProcess.stdout.on('data', async (data) => {
      !isTestDone && console.log(`stdout: ${data}`);
      if (data.includes('DB ready')) {
        setTimeout(() => {
          resolve(true);
        }, 2000);
      }
    });

    subServerProcess.stderr.on('data', (data) => {
      !isTestDone && console.error(`stderr: ${data}`);
    });

    subServerProcess.on('close', (code) => {
      !isTestDone && console.log(`child process exited with code ${code}`);
    });
  })

  server.setupTestDatabase();
  await server.onDBReady();
  console.log('DONE BEFORE SCRIPT');
})

describe('Multi server should up and running', () => {
  test('Call random api should return 200 response', async () => {
    mainApiClient.setHost(mainRegionEndpoint);
    const res = await mainApiClient.Api.User.me();
    expect(res.status).toBe(200);
    subApiClient.setHost(subRegionEndpoint);
    const res2 = await subApiClient.Api.User.me();
    expect(res2.status).toBe(200);
  });

  test('Call api regionInfo should return correct region', async () => {
    const mainRes = await mainApiClient.Api.Hello.regionInfo();
    expect(mainRes.status).toBe(200);
    expect(mainRes.data.region).toBe('us');
    expect(mainRes.data.thisServer).toBe(mainRegionEndpoint);

    const subRes = await subApiClient.Api.Hello.regionInfo();
    expect(subRes.status).toBe(200);
    expect(subRes.data.region).toBe('uk');
    expect(subRes.data.thisServer).toBe(subRegionEndpoint);
  });
});

describe('Client sync between regions', () => {
  test('Create a client in region main, it should show in region sub', async () => {
    // Login as main region admin
    await mainApiClient.Api.User.login({
      email: '<EMAIL>',
      password: 'CD%2023'
    });
    // create a client in main region
    const res = await mainApiClient.Api.Client.create({
      name: 'ClientTest1',
      logo: 'logo',
      location: 'location',
      data: {
        foo: 'bar',
      },
      slug: 'clienttest',
      region: 'uk',
    });
    // wait for a few seconds for the sync to happen
    await new Promise(resolve => {
      setTimeout(() => {
        resolve(true);
      }, 2000);
    });
    
    // Login as main sub admin
    await subApiClient.Api.User.login({
      email: '<EMAIL>',
      password: 'CD%2023'
    });
    // get the client in sub region
    const res2 = await subApiClient.Api.Client.detail({
      id: res.data.id,
    });
    expect(res2.status).toBe(200);
    expect(res2.data.id).toBe(res.data.id);
    expect(res2.data.region).toBe(res.data.region);
    expect(res2.data.slug).toBe(res.data.slug);
  });
});

afterAll(async () => {
  isTestDone = true;
  if (subServerProcess) {
    kill(subServerProcess.pid);
    subServerProcess.kill();
  }
  // await new Promise((resolve) => {
  //   setTimeout(() => {
  //     resolve(undefined);
  //   }, 2000);
  // })
  console.log('FORCE DROP DB TEST');
  if (subDBData) await DB.removeTestDBStandalone(subDBData.randomID);
  await server.removeTestDatabase();
})
