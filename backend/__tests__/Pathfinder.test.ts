import "dotenv/config";
require('cross-fetch');
import Server from "../server";
import apiClient from '../../api-client';
import { apiArrays } from '../apiArrays';
import { LocalStorage } from "node-localstorage";
import { MOCK_LEARNING_EXCEL, MOCK_PATHFINDER_EXCEL } from "helpers/TestImportData";
const port = Math.floor(Math.random() * 1000 + 2000);
const fs = require('fs');
const server = new Server(port);

let testClient;
let testClient2;

beforeAll(async () => {
  server.setupTestDatabase();
  server.initAPIs(apiArrays);
  await server.onDBReady();
  apiClient.setHost(`http://localhost:${port}`);
  global.localStorage = new LocalStorage('./node-localstorage-data');
  await apiClient.Api.User.login({
    email: '<EMAIL>',
    password: 'CD%2023'
  });
  const res = await apiClient.Api.Client.create({
    name: 'ClientTest',
    logo: 'logo',
    location: 'location',
    slug: 'clienttest',
    region: 'uk',
  });
  const res2 = await apiClient.Api.Client.create({
    name: 'ClientTest',
    logo: 'logo',
    location: 'location',
    slug: 'clienttest2',
    region: 'uk',
  });
  testClient = res.data.data;
  testClient2 = res2.data.data;
})


describe('as a Client Admin', () => {
  let testClientAdmin;
  test('Login client admin account', async () => {
    const res = await apiClient.Api.User.createAccount({
      email: '<EMAIL>',
      password: '123456',
      firstName: 'tester',
      role: 'client',
      clientId: testClient.id,
    });
    // create admin of other clients
    await apiClient.Api.User.createAccount({
      email: '<EMAIL>',
      password: '123456',
      firstName: 'tester',
      role: 'client',
      clientId: testClient2.id,
    });

    testClientAdmin = res.data.data;
    await apiClient.Api.User.login({
      email: testClientAdmin.email,
      password: '123456',
    })
  });

  let testPathfinder;
  test('I can create pathfinder', async () => {
    const params = {
      name: 'Frontend dev',
      welcomeInstruction: 'welcome',
      additionalInstruction: 'additional',
      completionInstruction: 'completed',
      likertScaleTitle1: 'title1',
      likertScaleTitle2: 'title2',
      likertScaleTitle3: 'title3',
      elementsTitle: 'elements title',
      disableElementsFilter: false,
    };
    const res = await apiClient.Api.Pathfinder.create(params);
    testPathfinder = res.data.data;
    expect(res.data.success).toBe(true);
    expect(res.data.data.clientId).toBe(testClientAdmin.clientId);
    Object.keys(params).map(key => {
      expect(res.data.data[key]).toBe(params[key]);
    });
  });

  test('I can update pathfinder', async () => {
    const res = await apiClient.Api.Pathfinder.update({
      id: testPathfinder.id,
      name: 'Backend dev',
      welcomeInstruction: 'Welcome',
    });
    expect(res.data.success).toBe(true);
    expect(res.data.data.name).toBe('Backend dev');
    expect(res.data.data.welcomeInstruction).toBe('Welcome');
    testPathfinder = res.data.data;
  });

  test('I can see all pathfinder', async () => {
    const res = await apiClient.Api.Pathfinder.list({});
    expect(res.data.success).toBe(true);
    expect(res.data.data.length).toBe(1);
    expect(res.data.data[0].name).toBe(testPathfinder.name);
  });

  test('I can not see pathfinders of other client', async () => {
    const res = await apiClient.Api.Pathfinder.list({
      clientId: testClient2.id,
    });
    expect(res.data.success).toBe(false);
    expect(res.data.error).toBe('Error: Permission denied');
  });

  test('I can add new pathfinder by importing file', async () => {
    const res = await apiClient.Api.Pathfinder.import({
      pathfinders: [MOCK_PATHFINDER_EXCEL],
      learnings: [MOCK_LEARNING_EXCEL],
    });
    expect(res.data.success).toBe(true);
    const res2 = await apiClient.Api.Pathfinder.list({
      name: MOCK_PATHFINDER_EXCEL.name,
    })
    expect(res2.data.data.length).toBe(1);
    expect(res2.data.data[0].likertScaleTitle1).toBe('Refresh');
    expect(res2.data.data[0].likertScaleTitle2).toBe('');
    expect(res2.data.data[0].likertScaleTitle3).toBe('Test Out');
    expect(res2.data.data[0].additionalInstruction).toBe(MOCK_PATHFINDER_EXCEL.additionalInstruction);

    const res3 = await apiClient.Api.Statement.list({
      pathfinderId: res2.data.data[0].id
    })
    expect(res3.data.data[0].learningsLikert1.length).toBe(1);
    expect(res3.data.data[0].learningsLikert1[0].name).toBe(MOCK_LEARNING_EXCEL.name);

    const res4 = await apiClient.Api.Element.list({
      pathfinderId: res2.data.data[0].id
    })
    expect(res4.data.data.length).toBe(1);
    expect(res4.data.data[0].name).toBe(MOCK_PATHFINDER_EXCEL.elements[0].name);
    expect(res3.data.data[0].elementIds).toBe(res4.data.data[0].id);
  });

  test('I can update pathfinder by importing file and not be duplicated', async () => {
    const res = await apiClient.Api.Pathfinder.import({
      pathfinders: [{
        ...MOCK_PATHFINDER_EXCEL,
        welcomeInstruction: "Edited via excel",
      }],
      learnings: [MOCK_LEARNING_EXCEL],
      updateExisting: true,
    });
    expect(res.data.success).toBe(true);
    const res2 = await apiClient.Api.Pathfinder.list({
      name: MOCK_PATHFINDER_EXCEL.name,
    })
    expect(res2.data.data.length).toBe(1);
    expect(res2.data.data[0].welcomeInstruction).toBe('Edited via excel');
    expect(res2.data.data[0].likertScaleTitle1).toBe('Refresh');

    const res3 = await apiClient.Api.Statement.list({
      pathfinderId: res2.data.data[0].id
    })
    expect(res3.data.data[0].learningsLikert1.length).toBe(1);
    expect(res3.data.data[0].learningsLikert1[0].name).toBe(MOCK_LEARNING_EXCEL.name);
  });

  test('I can export pathfinders', async () => {
    const res = await apiClient.Api.Pathfinder.export({});
    expect(res.data.success).toBe(true);
    const lastExported = res.data.data[0];
    expect(lastExported.name).toBe(MOCK_PATHFINDER_EXCEL.name);
    expect(lastExported.elements.length).toBe(MOCK_PATHFINDER_EXCEL.elements.length);
    expect(lastExported.statements.length).toBe(MOCK_PATHFINDER_EXCEL.statements.length);
  });

  test('I can remove pathfinder', async () => {
    const res = await apiClient.Api.Pathfinder.list({})
    const firstPathfinder = res.data.data[0]
    expect(firstPathfinder).toBeDefined();
    const res2 = await apiClient.Api.Pathfinder.remove({
      id: firstPathfinder.id,
    });
    expect(res2.data.success).toBe(true);
    const res3 = await apiClient.Api.Pathfinder.list({})
    expect(res3.data.data[0]).not.toBe(firstPathfinder.id);
  });

  test('I can not remove pathfinder of other client', async () => {
    const res = await apiClient.Api.Pathfinder.list({})
    const firstPathfinder = res.data.data[0]
    expect(firstPathfinder).toBeDefined();
    await apiClient.Api.User.login({
      email: '<EMAIL>',
      password: '123456',
    });
    const res2 = await apiClient.Api.Pathfinder.remove({
      id: firstPathfinder.id,
    });
    expect(res2.data.success).toBe(false);
    expect(res2.data.error).toBe('Error: Permission denied');
  });
});

describe('as a Global Admin', () => {
  test('I can duplicate Pathfinder to other client, and also copy Learning over', async () => {
    // login as Global Admin
    await apiClient.Api.User.login({
      email: '<EMAIL>',
      password: 'CD%2023'
    });
    // mock pathfinder
    await apiClient.Api.Pathfinder.import({
      pathfinders: [MOCK_PATHFINDER_EXCEL],
      learnings: [MOCK_LEARNING_EXCEL],
      clientId: testClient.id,
      updateExisting: true,
    });
    // check imported pathfinder
    let pathfinder;
    let learning;
    const res = await apiClient.Api.Pathfinder.list({
      name: MOCK_PATHFINDER_EXCEL.name,
      clientId: testClient.id,
    })
    pathfinder = res.data.data[0];
    expect(pathfinder).toBeDefined();
    // check learnings
    const res2 = await apiClient.Api.Learning.list({
      clientId: testClient.id,
    })
    learning = res2.data.data[0];
    expect(learning).toBeDefined();

    // duplicate pathfinder
    const res3 = await apiClient.Api.Pathfinder.duplicate({
      pathfinderId: pathfinder.id,
      clientId: testClient2.id,
    });
    expect(res3.data.success).toBe(true);

    // check duplicate pathfinder
    const res4 = await apiClient.Api.Pathfinder.list({
      name: MOCK_PATHFINDER_EXCEL.name,
      clientId: testClient2.id,
    })
    const newPathfinder = res4.data.data[0];
    expect(newPathfinder).toBeDefined();
    expect(newPathfinder.name).toBe(MOCK_PATHFINDER_EXCEL.name);
    expect(newPathfinder.additionalInstruction).toBe(MOCK_PATHFINDER_EXCEL.additionalInstruction);
    expect(newPathfinder.clientId).toBe(testClient2.id);

    const res5 = await apiClient.Api.Element.list({
      pathfinderId: newPathfinder.id,
    })
    expect(res5.data.data.length).toBe(MOCK_PATHFINDER_EXCEL.elements.length);
    expect(res5.data.data[0].name).toBe(MOCK_PATHFINDER_EXCEL.elements[0].name);

    const res6 = await apiClient.Api.Statement.list({
      pathfinderId: newPathfinder.id,
    })
    expect(res6.data.data.length).toBe(MOCK_PATHFINDER_EXCEL.statements.length);
    expect(res6.data.data[0].statement).toBe(MOCK_PATHFINDER_EXCEL.statements[0].statement);
    expect(res6.data.data[0].rolloverLikert1).toBe(MOCK_PATHFINDER_EXCEL.statements[0].rollover1);

    const res7 = await apiClient.Api.Learning.list({
      clientId: testClient2.id,
      name: MOCK_LEARNING_EXCEL.name,
    })
    expect(res7.data.data.length).toBe(1);
    expect(res7.data.data[0].name).toBe(MOCK_LEARNING_EXCEL.name);
    expect(res7.data.data[0].icon).toBe(MOCK_LEARNING_EXCEL.icon);
    expect(res7.data.data[0].url).toBe(MOCK_LEARNING_EXCEL.url);

  });
});

afterAll(async () => {
  await server.removeTestDatabase();
})
