import "dotenv/config";
require('cross-fetch');
import Server from "../server";
import apiClient from '../../api-client';
import { apiArrays } from '../apiArrays';
import { LocalStorage } from "node-localstorage";
const port = Math.floor(Math.random() * 1000 + 2000);
const fs = require('fs');
const server = new Server(port);

beforeAll(async () => {
  server.setupTestDatabase();
  server.initAPIs(apiArrays);
  await server.onDBReady();
  apiClient.setHost(`http://localhost:${port}`);
  global.localStorage = new LocalStorage('./node-localstorage-data');
})

describe('Server should up and running', () => {
  test('Call random api should return 200 response', async () => {
    const res = await apiClient.Api.User.me();
    expect(res.status).toBe(200);
  });
});

afterAll(async () => {
  await server.removeTestDatabase();
})