import "dotenv/config";
require('cross-fetch');
import Server from "../server";
import apiClient from '../../api-client';
import { apiArrays } from '../apiArrays';
import { LocalStorage } from "node-localstorage";
import RequestUtils from "../../api-client/Request.utils";
const port = +process.env.PORT || 3000;
const fs = require('fs');
const server = new Server(port);
const getRandom = () => Math.floor(Math.random() * 10000);

let otherClient;
beforeAll(async () => {
  server.setupTestDatabase();
  server.initAPIs(apiArrays);
  await server.onDBReady();
  apiClient.setHost(`http://localhost:${port}`);
  global.localStorage = new LocalStorage('./node-localstorage-data');
})

describe('Server should up and running', () => {
  test('Call random api should return 200 response', async () => {
    const res = await apiClient.Api.User.me();
    expect(res.status).toBe(200);
  });
})

describe('as a Global Admin', () => {
  let lastTestAccount

  test('I can login using my pre-account <EMAIL>', async () => {
    const res = await apiClient.Api.User.login({
      email: '<EMAIL>',
      password: 'CD%2023'
    });
    expect(res.status).toBe(200);
    const json = res.data;
    expect(json.data.token).toBeDefined();
    expect(json.data.publicInfo).toBeDefined();
    expect(json.data.publicInfo.password).toBeUndefined();
    expect(json.data.publicInfo.id).toBeDefined();
    expect(json.data.publicInfo.email).toBe('<EMAIL>');
  });

  test('after logging in, I can see my user profile, without exposing my password', async () => {
    const res = await apiClient.Api.User.me();
    expect(res.status).toBe(200);
    const json = res.data;
    expect(json.data).toBeDefined();
    expect(json.data.password).toBeUndefined();
    expect(json.data.id).toBeDefined();
    expect(json.data.email).toBe('<EMAIL>');
  });

  test('I can see list of all global admins', async () => {
    const res = await apiClient.Api.User.list({ clientId: null });
    expect(res.status).toBe(200);
    const json = res.data;
    expect(json.data).toBeDefined();
    expect(json.data.length).toBeGreaterThan(0);
    expect(json.data.some(i => i.clientId !== null)).toBe(false);
  });

  test('I can see list of all clients admins', async () => {
    const res = await apiClient.Api.User.list({ clientId: null, allClient: true });
    expect(res.status).toBe(200);
    expect(res.data.data.length).toBeGreaterThan(0);
    expect(res.data.data.some(i => i.clientId === null)).toBe(false);
  });

  test('I can see list of all clients', async () => {
    const res = await apiClient.Api.Client.list({});
    expect(res.status).toBe(200);
    expect(res.data.data.length).toBeGreaterThan(0);
  });

  test('I can create new client', async () => {
    const res = await apiClient.Api.Client.create({
      name: 'Client',
      location: 'Location',
      slug: 'client',
      region: 'uk',
    });
    expect(res.status).toBe(200);
    otherClient = res.data.data;
    expect(res.data.success).toBe(true);
    expect(res.data.data.name).toBe('Client');
  });

  test('I can update client', async () => {
    const res = await apiClient.Api.Client.update({
      id: otherClient.id,
      name: 'New Name',
      location: 'Location2',
    });
    expect(res.status).toBe(200);
    expect(res.data.success).toBe(true);
    expect(res.data.data.name).toBe('New Name');
    expect(res.data.data.location).toBe('Location2');
  });

  test('I can create admin client', async () => {
    const email = `demo${getRandom()}@gmail.com`;
    const res = await apiClient.Api.User.createAccount({
      firstName: 'First',
      lastName: 'Last',
      clientId: otherClient.id,
      email,
      password: '123456',
      role: 'client',
    });
    expect(res.status).toBe(200);
    expect(res.data.success).toBe(true);
    expect(res.data.data.email).toBe(email);
  });

  test('I can change password', async () => {
    const res = await apiClient.Api.User.changePassword({
      oldPassword: 'CD%2023',
      newPassword: '123456',
    });
    expect(res.status).toBe(200);
    const json = res.data;
    expect(json.success).toBe(true);

    const resLogin = await apiClient.Api.User.login({
      email: '<EMAIL>',
      password: 'CD%2023'
    });
    expect(resLogin.data.success).toBe(false);

    const resLogin2 = await apiClient.Api.User.login({
      email: '<EMAIL>',
      password: '123456'
    });
    expect(resLogin2.data.success).toBe(true);

    const resLogin3 = await apiClient.Api.User.changePassword({
      oldPassword: '123456',
      newPassword: 'CD%2023',
    });
    expect(resLogin3.data.success).toBe(true);
  });

  test('I can create other Global Admin account', async () => {
    const newEmail = `test${getRandom()}@devserver.london`;
    const res = await apiClient.Api.User.createAccount({
      email: newEmail,
      password: '123456',
      firstName: 'tester',
      lastName: '',
      role: 'admin',
    });
    expect(res.data.success).toBe(true);
    expect(res.data.data).toBeDefined();
    expect(res.data.data.password).toBeUndefined();
    expect(res.data.data.id).toBeDefined();
    expect(res.data.data.email).toBe(newEmail);
    lastTestAccount = res.data.data;
  });

  test('I can change other account password', async () => {
    const res = await apiClient.Api.User.changeAccountPassword({
      id: lastTestAccount?.id,
      password: 'newpassword',
    });
    expect(res.data.success).toBe(true);

    const resLogin = await apiClient.Api.User.login({
      email: lastTestAccount.email,
      password: '123456'
    });
    expect(resLogin.data.success).toBe(false);

    const resLogin2 = await apiClient.Api.User.login({
      email: lastTestAccount.email,
      password: 'newpassword'
    });
    expect(resLogin2.data.success).toBe(true);

    const res2 = await apiClient.Api.User.changeAccountPassword({
      id: lastTestAccount?.id,
      password: '123456',
    });
    expect(res2.data.success).toBe(true);
  });

  test('I can update Global Admin info', async () => {
    const res = await apiClient.Api.User.update({
      id: lastTestAccount.id,
      firstName: 'New Name',
      password: 'newpassword',
    });
    expect(res.data.success).toBe(true);
    expect(res.data.data).toBeDefined();
    expect(res.data.data.firstName).toBe('New Name');

    const res2 = await apiClient.Api.User.login({
      email: lastTestAccount.email,
      password: 'newpassword'
    });
    expect(res2.data.success).toBe(true);
  });

  test('I can delete Global Admin account', async () => {
    const res = await apiClient.Api.User.remove({
      id: lastTestAccount.id,
    });
    expect(res.data.success).toBe(true);

    const res2 = await apiClient.Api.User.login({
      email: lastTestAccount.email,
      password: 'newpassword'
    });
    expect(res2.data.success).toBe(false);
    expect(res2.data.error).toBe('Error: Email or password does not exist');
  });

  test('Request with wrong access token', async () => {
    RequestUtils.setToken('wrong token');
    const res = await apiClient.Api.Client.list({});
    expect(res.data.success).toBe(false);
  });

});

describe('as a Client Admin', () => {
  let testClientAdmin
  let testClientAdmin2
  test('I can login using my pre-account', async () => {
    const res = await apiClient.Api.User.login({
      email: '<EMAIL>',
      password: 'CD%2023'
    });
    expect(res.status).toBe(200);
    const json = res.data;
    expect(json.data.token).toBeDefined();
    testClientAdmin = json.data.publicInfo;
    expect(json.data.publicInfo.email).toBe('<EMAIL>');
  });

  test('I can update the account info', async () => {
    const res = await apiClient.Api.User.update({
      id: testClientAdmin.id,
      firstName: 'First Name 2'
    });
    expect(res.status).toBe(200);
    expect(res.data.data.firstName).toBe('First Name 2');
  });

  test('I can not create global admin account or admin of other client', async () => {
    const res = await apiClient.Api.User.createAccount({
      email: `demo${getRandom()}@gmail.com`,
      firstName: 'Demo',
      password: '123456',
      role: 'admin',
      clientId: otherClient?.id,
    });
    expect(res.data.success).toBe(false);
    expect(res.data.error).toBe('Error: Permission denied');
    const res2 = await apiClient.Api.User.createAccount({
      email: `demo${getRandom()}@gmail.com`,
      firstName: 'Demo',
      password: '123456',
      role: 'client',
      clientId: otherClient?.id,
    });
    expect(res2.data.success).toBe(false);
    expect(res2.data.error).toBe('Error: Permission denied');
  });

  test('I can create other account in my client', async () => {
    const email = `demo${getRandom()}@gmail.com`;
    const res = await apiClient.Api.User.createAccount({
      email,
      firstName: 'Demo',
      password: '123456',
      role: 'client',
      clientId: testClientAdmin.clientId,
    });
    expect(res.data.success).toBe(true);
    expect(res.data.data.email).toBe(email);
    testClientAdmin2 = res.data.data;
    const res3 = await apiClient.Api.User.login({
      email: testClientAdmin2.email,
      password: '123456',
    })
    expect(res3.data.success).toBe(true);
  });

  test('I can change the password', async () => {
    const res = await apiClient.Api.User.update({
      id: testClientAdmin2.id,
      password: '1234567',
    });
    expect(res.status).toBe(200);
    const res2 = await apiClient.Api.User.login({
      email: testClientAdmin2.email,
      password: '1234567'
    });
    expect(res2.status).toBe(200);
    expect(res2.data.data.token).toBeDefined();
  });

  test('I can delete the account', async () => {
    const res = await apiClient.Api.User.remove({
      id: testClientAdmin2.id,
    });
    expect(res.status).toBe(200);
    expect(res.data.success).toBe(true);
    const res2 = await apiClient.Api.User.login({
      email: testClientAdmin2.email,
      password: 'CD%2023'
    });
    expect(res2.data.success).toBe(false);
    expect(res2.data.error).toBe('Error: Email or password does not exist');
  });

});

afterAll(async () => {
  await server.removeTestDatabase();
})
