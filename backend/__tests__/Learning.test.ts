import "dotenv/config";
require('cross-fetch');
import Server from "../server";
import apiClient from '../../api-client';
import { apiArrays } from '../apiArrays';
import { LocalStorage } from "node-localstorage";
const port = Math.floor(Math.random() * 1000 + 2000);
const server = new Server(port);

let testClient;
beforeAll(async () => {
  server.setupTestDatabase();
  server.initAPIs(apiArrays);
  await server.onDBReady();
  apiClient.setHost(`http://localhost:${port}`);
  global.localStorage = new LocalStorage('./node-localstorage-data');

  await apiClient.Api.User.login({
    email: '<EMAIL>',
    password: 'CD%2023'
  });
  const res = await apiClient.Api.Client.create({
    name: 'ClientTest1',
    logo: 'logo',
    slug: 'clienttest',
    region: 'uk',
  });
  testClient = res.data.data;
})

describe('As Client Admin', () => {
  let testClientAdmin;
  test('Login client admin account', async () => {
    const res = await apiClient.Api.User.createAccount({
      email: '<EMAIL>',
      password: '123456',
      firstName: 'tester',
      role: 'client',
      clientId: testClient.id,
    });
    testClientAdmin = res.data.data;
    await apiClient.Api.User.login({
      email: testClientAdmin.email,
      password: '123456',
    })
  });

  let testLearning;
  test('I can create learning', async () => {
    const res = await apiClient.Api.Learning.create({
      name: 'Google',
      url: 'http://google.com',
      tags: 'a1,b2,a2',
    });
    testLearning = res.data.data;
    expect(res.data.success).toBe(true);
    expect(res.data.data.name).toBe('Google');
  });

  test('I can update learning', async () => {
    const res = await apiClient.Api.Learning.update({
      id: testLearning.id,
      name: 'Youtube',
      url: 'http://youtube.com',
    });
    expect(res.data.success).toBe(true);
    expect(res.data.data.name).toBe('Youtube');
    expect(res.data.data.url).toBe('http://youtube.com');
  });

  test('I can get all learnings of my client', async () => {
    const res = await apiClient.Api.Learning.list({});
    expect(res.data.success).toBe(true);
    expect(res.data.data.length).toBe(1);
    expect(res.data.data[0].name).toBe('Youtube');
  });

  test('I can not get learnings of other client', async () => {
    const res = await apiClient.Api.Learning.list({
      clientId: 'otherClient'
    });
    expect(res.data.success).toBe(false);
    expect(res.data.error).toBe('Error: Permission denied');
  });

  test('I can merge tags', async () => {
    const res = await apiClient.Api.Learning.list({});
    expect(res.data.success).toBe(true);
    expect(res.data.data.length).toBe(1);
    expect(res.data.data[0].tags).toBe('a1,b2,a2');

    await apiClient.Api.Learning.mergeTags({
      tags: 'a1,a2',
      newTag: 'A12',
    })

    const res2 = await apiClient.Api.Learning.list({});
    expect(res2.data.success).toBe(true);
    expect(res2.data.data.length).toBe(1);
    expect(res2.data.data[0].tags).toBe('b2,A12');
  });

  test('I can not create / update learning of other client', async () => {
    // mock other client data;
    let otherClient;
    let otherLearning;
    await apiClient.Api.User.login({
      email: '<EMAIL>',
      password: 'CD%2023'
    });
    const res = await apiClient.Api.Client.create({
      name: 'ClientTest2',
      logo: 'logo',
      slug: 'clienttest2',
      region: 'uk',
    });
    console.log('res.data', res.data);
    expect(res.data.success).toBe(true);
    otherClient = res.data.data;
    const res2 = await apiClient.Api.Learning.create({
      clientId: otherClient.id,
      name: 'Google',
      url: 'http://google.com',
    });
    expect(res2.data.success).toBe(true);
    otherLearning = res2.data.data;

    // login test client again
    await apiClient.Api.User.login({
      email: testClientAdmin.email,
      password: '123456',
    })

    // try create & update other client
    const res3 = await apiClient.Api.Learning.create({
      clientId: otherClient.id,
      name: 'Google',
      url: 'http://google.com',
    });
    expect(res3.data.success).toBe(false);
    expect(res3.data.error).toBe('Error: Permission denied');

    const res4 = await apiClient.Api.Learning.update({
      id: otherLearning.id,
      name: 'Google',
      url: 'http://google.com',
    });
    expect(res4.data.success).toBe(false);
    expect(res4.data.error).toBe('Error: Permission denied');
  });

});

afterAll(async () => {
  await server.removeTestDatabase();
})
