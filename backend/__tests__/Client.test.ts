import "dotenv/config";
require('cross-fetch');
import Server from "../server";
import apiClient from '../../api-client';
import { apiArrays } from '../apiArrays';
import { LocalStorage } from "node-localstorage";
const port = Math.floor(Math.random() * 1000 + 2000);
const fs = require('fs');
const server = new Server(port);

beforeAll(async () => {
  server.setupTestDatabase();
  server.initAPIs(apiArrays);
  await server.onDBReady();
  apiClient.setHost(`http://localhost:${port}`);
  global.localStorage = new LocalStorage('./node-localstorage-data');
  await apiClient.Api.User.login({
    email: '<EMAIL>',
    password: 'CD%2023'
  });
})

describe('Server should up and running', () => {
  test('Call random api should return 200 response', async () => {
    const res = await apiClient.Api.User.me();
    expect(res.status).toBe(200);
  });
})

describe('as a Global Admin', () => {
  let lastTestClient
  let lastClientAdmin

  test('I can create client', async () => {
    const res = await apiClient.Api.Client.create({
      name: 'ClientTest1',
      logo: 'logo',
      location: 'location',
      data: {
        foo: 'bar',
      },
      slug: 'clienttest',
      region: 'uk',
    });
    expect(res.data.success).toBe(true);
    expect(res.data.data.name).toBe('ClientTest1');
    expect(res.data.data.data.foo).toBe('bar');
    lastTestClient = res.data.data;
  });

  test('I can update client', async () => {
    const res = await apiClient.Api.Client.update({
      id: lastTestClient.id,
      name: 'ClientTest1',
      location: 'location2',
    });
    expect(res.data.success).toBe(true);
    expect(res.data.data.name).toBe(lastTestClient.name);
    expect(res.data.data.logo).toBe(lastTestClient.logo);
    expect(res.data.data.location).toBe('location2');
  });

  test('I can see all clients', async () => {
    const res = await apiClient.Api.Client.list({});
    expect(res.data.success).toBe(true);
    expect(res.data.data.length).toBe(2);
    expect(res.data.data[0].id).toBe(lastTestClient.id);
  });

  test('I can not create Client Admin with wrong clientId', async () => {
    const newEmail = '<EMAIL>';
    const res = await apiClient.Api.User.createAccount({
      email: newEmail,
      password: '123456',
      firstName: 'tester',
      lastName: '',
      role: 'client',
      clientId: 'wrongClientId',
    });
    expect(res.data.success).toBe(false);
  });

  test('I can create Client Admin', async () => {
    const newEmail = '<EMAIL>';
    const res = await apiClient.Api.User.createAccount({
      email: newEmail,
      password: '123456',
      firstName: 'tester',
      lastName: '',
      role: 'client',
      clientId: lastTestClient.id,
    });
    expect(res.data.success).toBe(true);
    expect(res.data.data.email).toBe(newEmail);
    lastClientAdmin = res.data.data;
  });

  test('Client admin can login', async () => {
    const res = await apiClient.Api.User.login({
      email: lastClientAdmin.email,
      password: '123456',
    });
    expect(res.data.success).toBe(true);
  });

  test('I can remove client', async () => {
    // login global admin again
    await apiClient.Api.User.login({
      email: '<EMAIL>',
      password: 'CD%2023'
    });
    const res = await apiClient.Api.Client.remove({
      id: lastTestClient.id,
    });
    expect(res.data.success).toBe(true);

    const listRes = await apiClient.Api.Client.list({});
    expect(listRes.data.success).toBe(true);
    expect(listRes.data.data.length).toBe(1);
  });

  test('I can see all clients - include the deleted', async () => {
    const res = await apiClient.Api.Client.list({
      all: true
    });
    expect(res.data.success).toBe(true);
    expect(res.data.data.length).toBe(2);
    expect(res.data.data[0].id).toBe(lastTestClient.id);
  });

  test('Client Admin can not login after client removed', async () => {
    const res = await apiClient.Api.User.login({
      email: lastClientAdmin.email,
      password: '123456',
    });
    expect(res.data.success).toBe(false);
  });

});


describe('as a Client Admin', () => {
  let lastTestClient
  let lastClientAdmin

  test('Global Admin create Client Admin', async () => {
    const res = await apiClient.Api.Client.create({
      name: 'ClientTest2',
      slug: 'clienttest',
      region: 'uk',
    });
    lastTestClient = res.data.data
    const res2 = await apiClient.Api.User.createAccount({
      email: '<EMAIL>',
      password: '123456',
      firstName: 'tester',
      role: 'client',
      clientId: lastTestClient.id,
    })
    lastClientAdmin = res2.data.data
  });
  
  test('I can login', async () => {
    const res = await apiClient.Api.User.login({
      email: lastClientAdmin.email,
      password: '123456',
    });
    expect(res.data.success).toBe(true);
    expect(res.data.data.publicInfo.email).toBe(lastClientAdmin.email);
  });
    
  test('I can update my client info', async () => {
    const newLocation = 'New Location'
    const res = await apiClient.Api.Client.update({
      location: newLocation,
    });
    expect(res.data.success).toBe(true);
    expect(res.data.data.location).toBe(newLocation);
  });

  test('I can not update other client info', async () => {
    const newLocation = 'New Location'
    const res = await apiClient.Api.Client.update({
      id: 'someId',
      location: newLocation,
    });
    expect(res.data.success).toBe(false);
    expect(res.data.error).toBe('Error: Permission denied');
  });

  test('I can create Admin for my client', async () => {
    const res = await apiClient.Api.Client.createAccounnt({
      email: '<EMAIL>',
      password: '123456',
      firstName: 'tester2',
    });
    expect(res.data.success).toBe(true);
    expect(res.data.data.email).toBe('<EMAIL>');
    expect(res.data.data.role).toBe('client');
    expect(res.data.data.clientId).toBe(lastTestClient.id);
  });

});

afterAll(async () => {
  await server.removeTestDatabase();
})
