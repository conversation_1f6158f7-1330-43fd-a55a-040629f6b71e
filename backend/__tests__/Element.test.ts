import "dotenv/config";
require('cross-fetch');
import Server from "../server";
import apiClient from '../../api-client';
import { apiArrays } from '../apiArrays';
import { LocalStorage } from "node-localstorage";
const port = Math.floor(Math.random() * 1000 + 2000);
const fs = require('fs');
const server = new Server(port);

let testClient;
beforeAll(async () => {
  server.setupTestDatabase();
  server.initAPIs(apiArrays);
  await server.onDBReady();
  apiClient.setHost(`http://localhost:${port}`);
  global.localStorage = new LocalStorage('./node-localstorage-data');
  await apiClient.Api.User.login({
    email: '<EMAIL>',
    password: 'CD%2023'
  });
  let res = await apiClient.Api.Client.create({
    name: 'ClientTest',
    logo: 'logo',
    location: 'location',
    slug: 'clienttest',
    region: 'uk',
  });
  testClient = res.data.data;
})


let testPathfinder;
describe('In specific Pathfinder, as a Client Admin', () => {
  let testElement;

  test('Login & create pathfinder', async () => {
    await apiClient.Api.User.createAccount({
      email: '<EMAIL>',
      password: '123456',
      firstName: 'tester',
      role: 'client',
      clientId: testClient.id,
    });
    await apiClient.Api.User.login({
      email: '<EMAIL>',
      password: '123456'
    });
    const res2 = await apiClient.Api.Pathfinder.create({
      name: 'Demo pathfinder',
    });
    testPathfinder = res2.data.data;
  });

  test('I can create element', async () => {
    const res = await apiClient.Api.Element.create({
      pathfinderId: testPathfinder.id,
      name: 'Element1',
      filterKeys: '1,2,3',
      data: {
        foo: 'bar',
      }
    });
    testElement = res.data.data;
    expect(res.data.success).toBe(true);
    expect(res.data.data.name).toBe('Element1');
    expect(res.data.data.data.foo).toBe('bar');
  });

  test('I can not create element without pathfinder', async () => {
    const res = await apiClient.Api.Element.create({
      pathfinderId: '',
      name: 'Element1',
    });
    expect(res.data.success).toBe(false);
  });

  test('I can update element', async () => {
    const res = await apiClient.Api.Element.update({
      id: testElement.id,
      filterKeys: '1,2,3,4',
      data: {
        bar: 'foo',
      }
    });

    expect(res.data.success).toBe(true);
    expect(res.data.data.name).toBe('Element1');
    expect(res.data.data.data.bar).toBe('foo');
    expect(res.data.data.filterKeys).toBe('1,2,3,4');
  });

  test('I can change order of elements & filter elements', async () => {
    let elements = [];
    const elms = [
      { idx: 1, keys: 'a,b,c' },
      { idx: 2, keys: 'c,a' },
      { idx: 2, keys: 'C, B' },
      { idx: 3, keys: 'b,a' },
    ]
    const res = await Promise.all(elms.map(async ({ idx, keys }) => {
      return await apiClient.Api.Element.create({
        pathfinderId: testPathfinder.id,
        name: `Element${idx}`,
        orderIndex: idx,
        filterKeys: keys,
      });
    }));
    elements = res.map(i => i.data.data);

    await apiClient.Api.Element.update({
      id: elements[2].id,
      orderIndex: 0,
    });
    const res2 = await apiClient.Api.Element.list({
      pathfinderId: testPathfinder.id,
    });
    expect(res2.data.data.length).toBe(elms.length + 1); // include 1 record created before
    expect(res2.data.data[0].name).toBe(elements[2].name);

    const res3 = await apiClient.Api.Element.list({
      pathfinderId: testPathfinder.id,
      filterKeys: 'b',
    });
    expect(res3.data.data.length).toBe(3);

    const res4 = await apiClient.Api.Element.list({
      pathfinderId: testPathfinder.id,
      filterKeys: 'a,c',
    });
    expect(res4.data.data.length).toBe(2);
  });

  test('I can remove element', async () => {
    const res = await apiClient.Api.Element.remove({
      id: testElement.id,
    });

    expect(res.data.success).toBe(true);
    const res2 = await apiClient.Api.Element.list({
      pathfinderId: testPathfinder.id,
    });

    expect(res2.data.data.some(i => i.id === testElement.id));
  });

})

afterAll(async () => {
  await server.removeTestDatabase();
})
