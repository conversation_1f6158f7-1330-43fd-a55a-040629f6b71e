import "dotenv/config";
require('cross-fetch');
import Server from "../server";
import apiClient from '../../api-client';
import { apiArrays } from '../apiArrays';
import { LocalStorage } from "node-localstorage";
import { MOCK_STATEMENT_EXCEL } from "helpers/TestImportData";
const port = Math.floor(Math.random() * 1000 + 2000);
const fs = require('fs');
const server = new Server(port);

let testClient;
let testPathfinder;
beforeAll(async () => {
  server.setupTestDatabase();
  server.initAPIs(apiArrays);
  await server.onDBReady();
  apiClient.setHost(`http://localhost:${port}`);
  global.localStorage = new LocalStorage('./node-localstorage-data');
  await apiClient.Api.User.login({
    email: '<EMAIL>',
    password: 'CD%2023'
  });
  const res = await apiClient.Api.Client.create({
    name: 'ClientTest',
    logo: 'logo',
    location: 'location',
    slug: 'clienttest',
    region: 'uk',
  });
  testClient = res.data.data;
  const res2 = await apiClient.Api.Pathfinder.create({
    name: 'PathfinderTest',
    clientId: testClient.id,
  });
  testPathfinder = res2.data.data;
})

describe('As Client Admin, in a specific Pathfinder', () => {
  let testClientAdmin;
  let testLearnings;
  test('Login client admin account', async () => {
    const res = await apiClient.Api.User.createAccount({
      email: '<EMAIL>',
      password: '123456',
      firstName: 'tester',
      role: 'client',
      clientId: testClient.id,
    });
    testClientAdmin = res.data.data;
    await apiClient.Api.User.login({
      email: testClientAdmin.email,
      password: '123456',
    })
    // mock Learnings
    testLearnings = await Promise.all(
      [0, 1, 2].map(async (idx) => {
        const resLearning = await apiClient.Api.Learning.create({
          name: `Learning${idx}`,
          url: 'http://google.com',
        });
        return resLearning.data.data;
      })
    )
  });

  test('I can not create Work Statement with wrong learning ids', async () => {
    const res = await apiClient.Api.Statement.create({
      elementIds: '1,2,3',
      learningIdsLikert1: 'wrongid',
      pathfinderId: testPathfinder.id,
      rolloverLikert1: 'Never',
      rolloverLikert2: 'Sometimes',
      rolloverLikert3: 'Always',
      statement: 'demo statement',
    });
    expect(res.data.success).toBe(false);
    expect(res.data.error).toBe('Error: Invalid learning ID');
  });

  let testStatement;
  test('I can create Work Statement', async () => {
    const learningIdsLikert1 = `${testLearnings[0].id}, ${testLearnings[1].id}`;
    const res = await apiClient.Api.Statement.create({
      elementIds: '1,2,3',
      learningIdsLikert1,
      learningIdsLikert2: `${testLearnings[2].id}`,
      pathfinderId: testPathfinder.id,
      rolloverLikert1: 'Never',
      rolloverLikert2: 'Sometimes',
      rolloverLikert3: 'Always',
      statement: 'demo statement',
    });
    testStatement = res.data.data;
    expect(res.data.success).toBe(true);
    expect(res.data.data.clientId).toBe(testClient.id);
    expect(res.data.data.learningIdsLikert1).toBe(learningIdsLikert1);
    expect(res.data.data.learningsLikert1[0].id).toBe(testLearnings[0].id);
    expect(res.data.data.learningsLikert1[1].id).toBe(testLearnings[1].id);
    expect(res.data.data.learningsLikert1[2]).toBe(undefined);
  });

  test('I can update Work Statement', async () => {
    const res = await apiClient.Api.Statement.update({
      id: testStatement.id,
      learningIdsLikert1: testLearnings[0].id,
      rolloverLikert1: 'Never2',
      statement: 'test statement',
    });
    testStatement = res.data.data;
    expect(res.data.success).toBe(true);
    expect(res.data.data.clientId).toBe(testClient.id);
    expect(res.data.data.rolloverLikert1).toBe('Never2');
    expect(res.data.data.learningIdsLikert1).toBe(testLearnings[0].id);
    expect(res.data.data.learningsLikert1[0].id).toBe(testLearnings[0].id);
    expect(res.data.data.learningsLikert1[1]).toBe(undefined);
  });

  test('In case there is an update of Learning, the Statement having that learning also updated', async () => {
    const resLearning = await apiClient.Api.Learning.update({
      id: testLearnings[0].id,
      name: 'Updated name',
    });
    expect(resLearning.data.success).toBe(true);
    expect(resLearning.data.data.name).toBe('Updated name');

    const resStatement = await apiClient.Api.Statement.list({
      pathfinderId: testPathfinder.id,
    });
    const isUpdated = resStatement.data.data[0].learningsLikert1.some(i => i.name === 'Updated name');
    expect(isUpdated).toBe(true);
  });

  test('I can change order of statements & filter statements', async () => {
    let statements = [];
    const elms = [
      { idx: 1, elementIds: 'a1,a2,a3' },
      { idx: 2, elementIds: 'c1,a1' },
      { idx: 3, elementIds: 'a1, b1' },
      { idx: 4, elementIds: 'b1, a2' },
    ]
    const res = await Promise.all(elms.map(async ({ idx, elementIds }) => {
      return await apiClient.Api.Statement.create({
        elementIds,
        pathfinderId: testPathfinder.id,
        statement: `demo statement ${idx}`,
        orderIndex: idx,
      });
    }));
    statements = res.map(i => i.data.data);

    await apiClient.Api.Statement.update({
      id: statements[2].id,
      orderIndex: 0,
    });
    const res2 = await apiClient.Api.Statement.list({
      pathfinderId: testPathfinder.id,
    });
    expect(res2.data.data.length).toBe(elms.length + 1); // include 1 record created before
    expect(res2.data.data[0].name).toBe(statements[2].name);

    const res3 = await apiClient.Api.Statement.list({
      pathfinderId: testPathfinder.id,
      elementId: 'a1',
    });
    expect(res3.data.data.length).toBe(3);

    const res4 = await apiClient.Api.Statement.list({
      pathfinderId: testPathfinder.id,
      elementId: 'a2',
    });
    expect(res4.data.data.length).toBe(2);

    const res5 = await apiClient.Api.Statement.list({
      pathfinderId: testPathfinder.id,
      elementId: 'a2',
      statement: 'demo statement 4',
    });
    expect(res5.data.data.length).toBe(1);
  });

  test('I can remove Work Statement', async () => {
    const res = await apiClient.Api.Statement.remove({
      id: testStatement.id,
    });
    expect(res.data.success).toBe(true);

    const res2 = await apiClient.Api.Statement.list({
      pathfinderId: testPathfinder.id,
    });
    expect(res2.data.data.some(i => i.id === testStatement.id)).toBe(false);
  });

  test('I can import Work Statement', async () => {
    const res = await apiClient.Api.Statement.import({
      pathfinderId: testPathfinder.id,
      statements: [MOCK_STATEMENT_EXCEL],
    });
    expect(res.data.success).toBe(true);
    const res2 = await apiClient.Api.Statement.list({
      pathfinderId: testPathfinder.id,
      statement: MOCK_STATEMENT_EXCEL.statement,
    });
    expect(res2.data.data.length).toBeGreaterThan(0);
  });
});

afterAll(async () => {
  await server.removeTestDatabase();
})
