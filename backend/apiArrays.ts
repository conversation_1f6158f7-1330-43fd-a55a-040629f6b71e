import * as <PERSON><PERSON><PERSON> from "./src/api/users";
import * as GeneralDataAP<PERSON> from "./src/api/generate-data";
import * as <PERSON>lientAP<PERSON> from "./src/api/clients";
import * as PathfinderAP<PERSON> from "./src/api/pathfinders";
import * as <PERSON><PERSON><PERSON><PERSON> from "./src/api/elements";
import * as State<PERSON><PERSON><PERSON> from "./src/api/statements";
import * as Learning<PERSON><PERSON> from "./src/api/learnings";
import * as <PERSON>rner<PERSON><PERSON> from "./src/api/learners";
import * as ImageAP<PERSON> from "./src/api/images";
import * as HelloWorldAPI from "./src/api/hello-world";
import * as MaintainerAP<PERSON> from "./src/api/maintainer";

export const apiArrays = [
  { name: 'user', apis: UsersAPI },
  { name: 'data', apis: GeneralDataAPI },
  { name: 'client', apis: ClientAPI },
  { name: 'pathfinder', apis: PathfinderAPI },
  { name: 'element', apis: ElementAPI },
  { name: 'statement', apis: StatementAPI },
  { name: 'learning', apis: LearningAPI },
  { name: 'learner', apis: LearnerAPI },
  { name: 'image', apis: ImageAPI },
  { name: 'hello', apis: HelloWorldAPI },
  { name: 'maintainer', apis: MaintainerAPI },
]
