import { apiArrays } from './apiArrays';

const fs = require('fs');
const path = require('path');

describe('Backend API and API Client code should be in sync', () => {

  test('Count api arrays', () => {
    const apiArrayLength = apiArrays.length;
    const apiClientFolder = path.join(__dirname, '../api-client/api');
    const files = fs.readdirSync(apiClientFolder);
    console.log('files length', files.length, apiArrayLength);
    expect(apiArrayLength + 2).toBe(files.length);
  });

});