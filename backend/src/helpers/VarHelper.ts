import { TAPIValidationError } from 'type';
import { v4 as uuid } from 'uuid';

require('es6-promise').polyfill();
require('isomorphic-fetch');
const { uniqueNamesGenerator, adjectives, animals } = require('unique-names-generator');

class VarHelper {

  unsplash;

  genId() {
    return uuid();
  }

  validateVar(input, joiSchema): undefined | TAPIValidationError {
    if (!joiSchema || !joiSchema.validate)
      return {
        details: [
          {
            message: "invalid schema",
            path: ["joiSchema"],
            type: "any",
            context: undefined,
          },
        ],
      };
    const { error } = joiSchema.validate(input);
    return error;
  }

  randomName = () => {
    const shortName = uniqueNamesGenerator({
      dictionaries: [adjectives, animals],
      separator: ' ',
      length: 2
    });

    return shortName;
  }

  removeUndefinedField = (obj) => {
    for (let key in obj) {
      if (obj[key] === undefined) {
        delete obj[key];
      }
    }
    return obj;
  }

  fourDigitsNumber = (n) => {
    if (n > 1000) return String(n);
    if (n > 100) return '0' + String(n);
    if (n > 10) return '00' + String(n);
    return '000' + String(n);
  }

  regexReplaceArray = (inputString: string, searchArray: string[], replacement: string) => {
    const pattern = new RegExp(searchArray.join('|'), 'g');
    const result = inputString.replace(pattern, replacement);
    if (result.split(',').filter(i => i === replacement).length > 1) {
      return result.split(',').filter(i => i !== replacement).concat(replacement).join(',');
    }
    return result;
  }

  replacePlaceholders = (inputString, replacements) => {
    if (!inputString || !replacements) return inputString;
    const regexPattern = /{{\s*\.(\w+)\s*}}/g;
    return inputString.replace(regexPattern, (match, placeholder) => {
      return replacements.hasOwnProperty(placeholder) ? replacements[placeholder] : match;
    });
  }

  genShortId = (length) => {
    let result = '';
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_';
    const charactersLength = characters.length;
    let counter = 0;
    while (counter < length) {
      result += characters.charAt(Math.floor(Math.random() * charactersLength));
      counter += 1;
    }
    return result;
  }

  getEndpointByRegion = (region: string) => {
    const thisServer = global.isMainServer ? global.mainRegionEndpoint : global.subRegionEndpoint;
    const otherServer = !global.isMainServer ? global.mainRegionEndpoint : global.subRegionEndpoint;
    if (region === global.region) {
      return thisServer;
    }
    return otherServer;
  }

}

export default new VarHelper();
