<?xml version = "1.0" encoding = "UTF-8"?>
<!--Generated by Turbo XML 2.3.1.100. Conforms to w3c http://www.w3.org/2001/XMLSchema-->
<xs:schema xmlns = "http://www.imsglobal.org/xsd/imsss"
	 targetNamespace = "http://www.imsglobal.org/xsd/imsss"
	 xmlns:xs = "http://www.w3.org/2001/XMLSchema"
	 elementFormDefault = "qualified"
	 attributeFormDefault = "unqualified">
	<xs:include schemaLocation = "imsss_v1p0util.xsd"/>
	<xs:complexType name = "auxiliaryResourceType">
		<xs:attribute name = "auxiliaryResourceID" use = "required" type = "xs:anyURI"/>
		<xs:attribute name = "purpose" use = "required" type = "xs:string"/>
	</xs:complexType>
	<xs:complexType name = "auxiliaryResourcesType">
		<xs:sequence>
			<xs:element name = "auxiliaryResource" type = "auxiliaryResourceType"
				 block = "#all" minOccurs = "0" maxOccurs = "unbounded"/>
		</xs:sequence>
	</xs:complexType>
</xs:schema>