<!-- saved from url=(0014)about:internet -->
<html lang="en">

<!-- 
Smart developers always View Source. 

This application was built using Adobe Flex, an open source framework
for building rich Internet applications that get delivered via the
Flash Player or to desktops via Adobe AIR. 

Learn more about Flex at http://flex.org 
// -->

<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />

	<!--  BEGIN Browser History required section -->
	<!--<link rel="stylesheet" type="text/css" href="history/history.css" />-->
	<!--  END Browser History required section -->

	<title></title>
	<!--  BEGIN Browser History required section -->
	<!--<script src="history/history.js" language="javascript"></script>-->
	<!--  END Browser History required section -->
	<script language="JavaScript" src="scormfunctions.js"></script>
	<style>
		html,
    body {
      width: 100%;
      height: 100%;
      margin: 0;
      padding: 0;
			overflow: hidden;
    }
	</style>
	<script language="JavaScript" type="text/javascript">
		function closeWindow() {
			ScormProcessSetValue("cmi.completion_status", "completed");
			parent.CloseApplication();
		}
		var currentPage = null;
		var startTimeStamp = null;
		var processedUnload = false;
		var reachedEnd = false;
		
		function doStart(){
			//record the time that the learner started the SCO so that we can report the total time
			startTimeStamp = new Date();
			
			//initialize communication with the LMS
			ScormProcessInitialize();
			
			//it's a best practice to set the completion status to incomplete when
			//first launching the course (if the course is not already completed)
			var completionStatus = ScormProcessGetValue("cmi.completion_status", true);
			if (completionStatus == "unknown"){
				ScormProcessSetValue("cmi.completion_status", "incomplete");
			}
		}

		function ConvertMilliSecondsIntoSCORM2004Time(intTotalMilliseconds){
	
			var ScormTime = "";
			
			var HundredthsOfASecond;	//decrementing counter - work at the hundreths of a second level because that is all the precision that is required
			
			var Seconds;	// 100 hundreths of a seconds
			var Minutes;	// 60 seconds
			var Hours;		// 60 minutes
			var Days;		// 24 hours
			var Months;		// assumed to be an "average" month (figures a leap year every 4 years) = ((365*4) + 1) / 48 days - 30.4375 days per month
			var Years;		// assumed to be 12 "average" months
			
			var HUNDREDTHS_PER_SECOND = 100;
			var HUNDREDTHS_PER_MINUTE = HUNDREDTHS_PER_SECOND * 60;
			var HUNDREDTHS_PER_HOUR   = HUNDREDTHS_PER_MINUTE * 60;
			var HUNDREDTHS_PER_DAY    = HUNDREDTHS_PER_HOUR * 24;
			var HUNDREDTHS_PER_MONTH  = HUNDREDTHS_PER_DAY * (((365 * 4) + 1) / 48);
			var HUNDREDTHS_PER_YEAR   = HUNDREDTHS_PER_MONTH * 12;
			
			HundredthsOfASecond = Math.floor(intTotalMilliseconds / 10);
			
			Years = Math.floor(HundredthsOfASecond / HUNDREDTHS_PER_YEAR);
			HundredthsOfASecond -= (Years * HUNDREDTHS_PER_YEAR);
			
			Months = Math.floor(HundredthsOfASecond / HUNDREDTHS_PER_MONTH);
			HundredthsOfASecond -= (Months * HUNDREDTHS_PER_MONTH);
			
			Days = Math.floor(HundredthsOfASecond / HUNDREDTHS_PER_DAY);
			HundredthsOfASecond -= (Days * HUNDREDTHS_PER_DAY);
			
			Hours = Math.floor(HundredthsOfASecond / HUNDREDTHS_PER_HOUR);
			HundredthsOfASecond -= (Hours * HUNDREDTHS_PER_HOUR);
			
			Minutes = Math.floor(HundredthsOfASecond / HUNDREDTHS_PER_MINUTE);
			HundredthsOfASecond -= (Minutes * HUNDREDTHS_PER_MINUTE);
			
			Seconds = Math.floor(HundredthsOfASecond / HUNDREDTHS_PER_SECOND);
			HundredthsOfASecond -= (Seconds * HUNDREDTHS_PER_SECOND);
			
			if (Years > 0) {
				ScormTime += Years + "Y";
			}
			if (Months > 0){
				ScormTime += Months + "M";
			}
			if (Days > 0){
				ScormTime += Days + "D";
			}
			
			//check to see if we have any time before adding the "T"
			if ((HundredthsOfASecond + Seconds + Minutes + Hours) > 0 ){
				
				ScormTime += "T";
				
				if (Hours > 0){
					ScormTime += Hours + "H";
				}
				
				if (Minutes > 0){
					ScormTime += Minutes + "M";
				}
				
				if ((HundredthsOfASecond + Seconds) > 0){
					ScormTime += Seconds;
					
					if (HundredthsOfASecond > 0){
						ScormTime += "." + HundredthsOfASecond;
					}
					
					ScormTime += "S";
				}
				
			}
			
			if (ScormTime == ""){
				ScormTime = "0S";
			}
			
			ScormTime = "P" + ScormTime;
			
			return ScormTime;
		}

		function doUnload(pressedExit){
			
			//don't call this function twice
			if (processedUnload == true){return;}
			
			processedUnload = true;
			
			//record the session time
			var endTimeStamp = new Date();
			var totalMilliseconds = (endTimeStamp.getTime() - startTimeStamp.getTime());
			var scormTime = ConvertMilliSecondsIntoSCORM2004Time(totalMilliseconds);
			
			ScormProcessSetValue("cmi.session_time", scormTime);
			
			//always default to saving the runtime data in this example
			ScormProcessSetValue("cmi.exit", "suspend");
			
			ScormProcessTerminate();
		}
	</script>
</head>

<body scroll="no" onload="doStart(false);" onbeforeunload="doUnload(false);" onunload="doUnload();">
	<form id="frmjson" method="POST">
		<input type="hidden" id="json" value="swfdata" />
	</form>
	<iframe src="{{pathfinderUrl}}?scorm=1" style="width: 100%; height:100%;"></iframe>
	<script>
		function closeScorm() {
			ScormProcessSetValue("cmi.completion_status", "completed");
			closeWindow();
		}
		function updateLessonStatus(status) {
			ScormProcessSetValue("cmi.completion_status", status);
		}

		window.addEventListener('message', function(event) {
			if (event.data.event_id === 'close_scorm') {
				closeScorm();
			}
			if (event.data.event_id === 'update_lesson_status') {
				updateLessonStatus(event.data.status || "completed");
			}
		});
	</script>
</body>

</html>
