<?xml version = "1.0" encoding = "UTF-8"?>
<!--Generated by Turbo XML 2.3.1.100. Conforms to w3c http://www.w3.org/2001/XMLSchema-->
<xs:schema xmlns = "http://www.imsglobal.org/xsd/imsss"
	 targetNamespace = "http://www.imsglobal.org/xsd/imsss"
	 xmlns:xs = "http://www.w3.org/2001/XMLSchema"
	 elementFormDefault = "qualified"
	 attributeFormDefault = "unqualified">
	
	<xs:complexType name = "deliveryControlsType">
		<xs:annotation>
			<xs:documentation>The type that describes any element which fullfills a delivery control semantic</xs:documentation>
		</xs:annotation>
		<xs:attribute name = "tracked" default = "true" type = "xs:boolean"/>
		<xs:attribute name = "completionSetByContent" default = "false" type = "xs:boolean"/>
		<xs:attribute name = "objectiveSetByContent" default = "false" type = "xs:boolean"/>
	</xs:complexType>
</xs:schema>