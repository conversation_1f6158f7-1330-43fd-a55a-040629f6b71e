<?xml version = "1.0" encoding = "UTF-8"?>
<!--Generated by Turbo XML 2.3.1.100. Conforms to w3c http://www.w3.org/2001/XMLSchema-->
<xs:schema xmlns = "http://www.imsglobal.org/xsd/imsss"
	 targetNamespace = "http://www.imsglobal.org/xsd/imsss"
	 xmlns:xs = "http://www.w3.org/2001/XMLSchema"
	 elementFormDefault = "qualified"
	 attributeFormDefault = "unqualified">
	<xs:include schemaLocation = "imsss_v1p0seqrule.xsd"/>
	<xs:include schemaLocation = "imsss_v1p0objective.xsd"/>
	<xs:include schemaLocation = "imsss_v1p0delivery.xsd"/>
	<xs:include schemaLocation = "imsss_v1p0random.xsd"/>
	<xs:include schemaLocation = "imsss_v1p0rollup.xsd"/>
	<xs:include schemaLocation = "imsss_v1p0control.xsd"/>
	<xs:include schemaLocation = "imsss_v1p0limit.xsd"/>
	<xs:include schemaLocation = "imsss_v1p0auxresource.xsd"/>
	<xs:element name = "sequencing" type = "sequencingType"
		 block = "#all">
		<xs:annotation>
			<xs:documentation>The root element for all sequencing tags.  This tag will usually appear as a child element to an IMS CP item tag.</xs:documentation>
		</xs:annotation>
	</xs:element>
	<xs:complexType name = "sequencingType">
		<xs:annotation>
			<xs:documentation>The type associated with any top-level sequencing tag</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name = "controlMode" type = "controlModeType"
				 block = "#all" minOccurs = "0">
				<xs:annotation>
					<xs:documentation>non-exclusive definition of acceptable control-modes</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name = "sequencingRules" type = "sequencingRulesType"
				 block = "#all" minOccurs = "0"/>
			<xs:element name = "limitConditions" type = "limitConditionsType"
				 block = "#all" minOccurs = "0"/>
			<xs:element name = "auxiliaryResources" type = "auxiliaryResourcesType"
				 block = "#all" minOccurs = "0"/>
			<xs:element name = "rollupRules" type = "rollupRulesType"
				 block = "#all" minOccurs = "0"/>
			<xs:element name = "objectives" type = "objectivesType"
				 block = "#all" minOccurs = "0">
<!--
				<xs:unique name = "uniqueGlobalObjective">
					<xs:selector xpath = ".//imsss:mapInfo[@writeSatisfiedStatus = 'true' or @writeNormalizedMeasure = 'true']"/>
					<xs:field xpath = "@targetObjectiveID"/>
				</xs:unique>
-->
			</xs:element>
			<xs:element name = "randomizationControls" type = "randomizationType"
				 block = "#all" minOccurs = "0"/>
			<xs:element name = "deliveryControls" type = "deliveryControlsType"
				 block = "#all" minOccurs = "0"/>
			<xs:any namespace = "##other" processContents = "strict" minOccurs = "0" maxOccurs = "unbounded"/>
		</xs:sequence>
		<xs:attribute name = "ID" type = "xs:ID"/>
		<xs:attribute name = "IDRef" type = "xs:IDREF"/>
	</xs:complexType>
	<xs:element name = "sequencingCollection"
		 block = "#all">
		<xs:complexType>
			<xs:sequence>
				<xs:element ref = "sequencing" maxOccurs = "unbounded"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
</xs:schema>