<?xml version = "1.0" encoding = "UTF-8"?>
<!--Generated by Turbo XML 2.3.1.100. Conforms to w3c http://www.w3.org/2001/XMLSchema-->
<xs:schema xmlns = "http://www.imsglobal.org/xsd/imsss"
	 targetNamespace = "http://www.imsglobal.org/xsd/imsss"
	 xmlns:xs = "http://www.w3.org/2001/XMLSchema"
	 elementFormDefault = "qualified"
	 attributeFormDefault = "unqualified">
	<xs:include schemaLocation = "imsss_v1p0util.xsd"/>
	
	<xs:complexType name = "randomizationType">
		<xs:attribute name = "randomizationTiming" default = "never" type = "randomTimingType"/>
		<xs:attribute name = "selectCount" type = "xs:nonNegativeInteger"/>
		<xs:attribute name = "reorderChildren" default = "false" type = "xs:boolean"/>
		<xs:attribute name = "selectionTiming" default = "never" type = "randomTimingType"/>
	</xs:complexType>
</xs:schema>