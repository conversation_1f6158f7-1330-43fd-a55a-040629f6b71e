require('dotenv').config();
const path = require('path');
const fs = require('fs');
const axios = require('axios');

import AWSHelper from './AWSHelper';
import FileHelper from './FileHelper';

test('upload file to S3', async () => {
  const filePath = path.join(__dirname, 'test-file.txt');
  if (!fs.existsSync(filePath)) {
    throw new Error(`File not found at path: ${filePath}`);
  }

  const url = await AWSHelper.upload({
    filePath,
    key: `test-file-${new Date().getTime()}`
  })
  
  // const fileContent = await FileHelper.readFileFromURL(url)
  const response = await axios.get(url);
  expect(response.data).toEqual('ABCXYZ');
  console.log('response.headers', response.headers);
  expect(response.headers["content-type"]).toEqual('text/plain');
});


test('upload file from URL', async () => {
  const url = await AWSHelper.uploadFileFromURL({
    url: 'https://fastly.picsum.photos/id/229/50/50.jpg?hmac=lFMJWcM6L__Z_tfxjHJQA5mxm_2Rr5KHLlrkS5xzU5k',
    key: `test-img-${new Date().getTime()}`
  })
  expect(url).toBeDefined();
  const response = await axios.get(url);
  expect(response.headers["content-type"]).toEqual('image/jpeg');
});
