const fs = require('fs');
const axios = require('axios');
const path = require('path');
const AdmZip = require('adm-zip');

class FileHelper {

  fileToBase64(filePath) {
    const buffer = fs.readFileSync(filePath);
    const base64 = Buffer.from(buffer).toString('base64');
    return base64;
  }

  base64ToFile(base64Data, filePath) {
    fs.writeFileSync(filePath, base64Data, 'base64');
  }

  async downloadFile(fileUrl, outputLocationPath, otherAxiosOptions = {}) {
    const writer = fs.createWriteStream(outputLocationPath);

    return new Promise((resolve, reject) => {
      axios({
        method: 'get',
        url: fileUrl,
        responseType: 'stream',
        ...otherAxiosOptions,
      }).then(response => {
        response.data.pipe(writer);
        let error = null;
        writer.on('error', err => {
          error = err;
          writer.close();
          reject(err);
        });
        writer.on('close', () => {
          if (!error) {
            resolve(true);
          }
        });
      }).catch(err => {
        reject(err);
      });
    });
  }

  async readFileFromURL(url) {
    try {
      const response = await axios.get(url);
      return response.data;
    } catch (error) {
      throw new Error(`Failed to fetch file from URL: ${url}`);
    }
  }

  editTemplateSCORM = (info: { pathfinderUrl: string, pathfinderName: string }, templateName: 'scorm-1.2' | 'scorm-2004', outputFileName) => {
    const templateZipPath = path.join(__dirname, templateName + '.zip');
    const outputPath = path.join(__dirname, '../uploads', outputFileName + '.zip');

    if (!fs.existsSync(templateZipPath)) {
      throw new Error('Template ZIP file not found.');
    }
    if (fs.existsSync(outputPath)) {
      return `b/public/${outputFileName}.zip`;
    }

    const zip = new AdmZip();

    const templateFolderPath = path.join(__dirname, templateName);

    function addFolderToZip(folderPath, relativePath) {
      const files = fs.readdirSync(folderPath);
      files.forEach(file => {
        const filePath = path.join(folderPath, file);
        const fileRelativePath = path.join(relativePath, file);
        if (fs.statSync(filePath).isDirectory()) {
          addFolderToZip(filePath, fileRelativePath);
        } else {
          let fileContent = fs.readFileSync(filePath);

          if (file === 'Capensys.html' || file === 'imsmanifest.xml' || file === "index.html" || file === "Index.html") {
            Object.keys(info).forEach((key) => {
              const regex = new RegExp(`{{${key}}}`, 'g');
              fileContent = fileContent.toString('utf-8').replace(regex, info[key]);
              fileContent = Buffer.from(fileContent, 'utf-8');;
            });
          }

          zip.addFile(fileRelativePath, fileContent);
        }
      });
    }

    addFolderToZip(templateFolderPath, "");

    zip.writeZip(outputPath);

    setTimeout(() => {
      fs.unlinkSync(outputPath);
    }, 60000 * 2);

    return `b/public/${outputFileName}.zip`;
  }


}

export default new FileHelper();
