require('dotenv').config();
const path = require('path');
const fs = require('fs');

import FileHelper from './FileHelper';

test('convert file to base64', async () => {
  const filePath = path.join(__dirname, 'test-file.txt');
  const res = await FileHelper.fileToBase64(filePath);
  expect(res).toEqual('QUJDWFla')
});

test('convert base64 to file', async () => {
  const filePath = path.join(__dirname, `test-file-${new Date().getTime()}.txt`);
  await FileHelper.base64ToFile('QUJDWFla', filePath);
  const res = fs.readFileSync(filePath, 'utf-8')
  expect(res).toEqual('ABCXYZ')
  fs.unlinkSync(filePath)
})

test('download file', async () => {
  const url = 'https://pathfinder-cd.fra1.digitaloceanspaces.com/files/1692093278341-test-file.txt'
  const filePath = path.join(__dirname, `downloed-file.txt`);
  await FileHelper.downloadFile(url, filePath);
  if (!fs.existsSync(filePath)) {
    throw new Error(`File not found at path: ${filePath}`);
  }
  fs.unlinkSync(filePath)
})
