<?xml version="1.0" standalone="no" ?>
<!--
Single SCO with basic runtime calls. SCORM 2004 3rd Edition.

Provided by Rustici Software - http://www.scorm.com

This example demonstrates the use of basic runtime calls in a multi-page SCO. It
includes a demonstration of bookmarking, status reporting (completion and success), 
score and time. It also includes the addition of a basic "controller" for providing
intra-SCO navigation.
-->

<manifest identifier="MANIFEST_sys" version="1"
          xmlns="http://www.imsproject.org/xsd/imscp_rootv1p1p2" 
		  xmlns:adlcp="http://www.adlnet.org/xsd/adlcp_rootv1p2" 
		  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
		  xsi:schemaLocation="http://www.imsproject.org/xsd/imscp_rootv1p1p2 imscp_rootv1p1p2.xsd 
							  http://www.imsglobal.org/xsd/imsmd_rootv1p2p1 imsmd_rootv1p2p1.xsd 
							  http://www.adlnet.org/xsd/adlcp_rootv1p2 adlcp_rootv1p2.xsd">
	
  <metadata>
		<schema>ADL SCORM</schema>
		<schemaversion>2004 3rd Edition</schemaversion>
	</metadata>
  <organizations default="MANIFESTDC_ORG_0001">
		<organization identifier="MANIFESTDC_ORG_0001" adlseq:objectivesGlobalToSystem="false">
			<title>{{pathfinderName}}</title>
			<item identifier="item_1" identifierref="resource_1">
				<title>{{pathfinderName}}</title>
        <!-- Include some basic sequencing information that overrides the defaults-->
        <imsss:sequencing>
          <imsss:deliveryControls completionSetByContent="true" objectiveSetByContent="true"/>
        </imsss:sequencing>
			</item>
      <imsss:sequencing>
        <imsss:controlMode choice="true" flow="true"/>
      </imsss:sequencing>
		</organization>
	</organizations>
	<resources>
		<resource identifier="resource_1" type="webcontent" href="Index.html" adlcp:scormtype="sco">
      <file href="Index.html" />
			<file href="Content/scormfunctions.js"/>
		</resource>
	</resources>
</manifest>
