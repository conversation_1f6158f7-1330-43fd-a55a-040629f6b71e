/* CSS file */

/* Character button text style*/
.labelTextStyle{
	font-size:12;
	color: #9e1323;
	text-align:center;	
	border-thickness: 0;
	background-alpha: 0;
	focus-alpha: 0;

}

.pathfinderNameStyle{
	font-size:12;
	fontWeight:bold;
	color: #9e1323;
	text-align:center;	
	border-thickness: 0;
	background-alpha: 0;
	focus-alpha: 0;
	
}
/* focusAlpha="0" focusRect="false" editable="false"  selectable="false" */
/* Character button style*/ 
.charButtonStyle{
	rollOverEffect: glowImage;
	rollOutEffect: unglowImageFast;        
}
.sliderPageBackBtnStyle{
    upSkin: Embed("assets/back.png");
 	overSkin: Embed("assets/back_rollover.png");
    downSkin: Embed("assets/back_rollover.png");
    disabledSkin: Embed("assets/back.png");    
	focus-alpha: 0;
}
.sliderPageNextBtnStyle{
    upSkin: Embed("assets/next.png");
 	overSkin: Embed("assets/next_rollover.png");
    downSkin: Embed("assets/next_rollover.png");
    disabledSkin: Embed("assets/next.png");    
	focus-alpha: 0;
}
.hSliderThumbStyle{
	thumb-skin: Embed("assets/Slider_thumb.png");
	track-skin: Embed("assets/Sliding_bar.png");
}

.showTip {
    fontSize: 12;
    borderSkin: ClassReference("com.controls.MyToolTip");
}