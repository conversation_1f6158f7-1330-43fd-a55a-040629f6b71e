/*--------------------------------------------------------------------------------------------------/
Updated By: Sachin Samel
Programmed on: 02-Dec-2009
Current Version: 1.0
--------------------------------------------------------------------------------------------------*/

var findAPITries=1;
var completionsent=false
var strCourseCompletion="";
var pageByPageStatus="";
var lessonLocation="";
var suspendedData="";
var studentName;
var courseScore;
var logInTime;
var sessionTime;
var myBookMarkString="";
var API = null;
var ret;
var code;
var diag;
var isInitiated = false;

//-------------------- This function intiates the SCO--------------------//
function initSco()
{
	API = GetAPI();

	if( API != null )
	{
		API.LMSInitialize("");
		
		startTimer();

		var status = get_val("cmi.core.lesson_status");
		var strloc = get_val("cmi.core.lesson_location");
		lessonLocation = strloc;

		if (status == "not attempted")
		{
			set_val("cmi.core.lesson_status","incomplete");
		}

		isInitiated = true;

		fnCheckforErrors();
	}	
	return true;
}

//--------------------- Function to Get the API ------------------------------------------------//
function GetAPI()
{
   var API = FindAPI(window);
   if ((API == null) && (window.opener != null) && (typeof(window.opener) != "undefined"))
   {
      API = FindAPI(window.opener);
   }
   if (API == null)
   {
	// alert("Course is not able to find the API and the course will not communicate with the LMS. No data will be tracked.")
     parent.status = "Unable to find an API adapter";
   }
   return API
}

//---------------------- Function to Find API -----------------------------------------------//
function FindAPI(win)
{
   while ((win.API == null) && (win.parent != null) && (win.parent != win))
   {
      findAPITries++;
      // Note: 7 is an arbitrary number, but should be more than sufficient
      if (findAPITries > 7) 
      {
        parent.status = "Error finding API -- too deeply nested.";
         return null;
      }      
      win = win.parent;
   }
   return win.API;
}

//----------------------- Function to check any error in LMS communication -------------------//
function fnCheckforErrors()
{
	code = API.LMSGetLastError();
	ret = API.LMSGetErrorString(code);	

	if(code!=0)
	{
		alert("Error: "+ret)
	}

	diag = API.LMSGetDiagnostic("");		
}

//------------------------- Function to get the value from LMS ---------------------------------//
function get_val(gname)
{	
	var returnValue;

	API = GetAPI();

	if(API != null)
	{	
		returnValue = API.LMSGetValue(gname);		
		fnCheckforErrors();		
	}

	return returnValue;
};

//------------------------- Function to set the value on LMS ------------------------------------//
function set_val(gname, gvalue)
{
	API = GetAPI();

	if( API != null )
	{		
		API.LMSSetValue(gname, gvalue);		
		fnCheckforErrors();
	}

	commit();
};

//--------------------------- Function to commit the value on LMS -------------------------------//
function commit()
{	
	API = GetAPI();

	if(API != null)
	{		
		API.LMSCommit("");
		fnCheckforErrors();
	}
	
};
//--------------------------- Function to teminate the communication with LMS -------------------//
function finish()
{
	sessionTime = getTimeSpent();

	set_val("cmi.core.session_time", sessionTime);
	
	commit();

	API = GetAPI();			
	if( API != null)
	{
		ret = API.LMSFinish("");
	}
	
};

//---------------------------- Function to send the completion status to LMS ---------------------//
function fnSendCompletion(param)
{
	if (param == "completed")
	{
		set_val("cmi.core.lesson_status", "completed");
	}
	else if(param == "incomplete")
	{
		set_val("cmi.core.lesson_status", "incomplete");
	}
}

//-----------------  THIS IS  -----------------------------------------------------
function fnSetBookMark_Completion(bml,completion)
{
	strCourseCompletion = bml	
	if (completion == "1")
	{
		fnSendCompletion(strCourseCompletion)
	} 
}

//-----------------  Function to set the bookmarking data on LMS  ----------------------------------//
function fnSetBookMark(bookMarkDataString)
{
	//alert(bookMarkDataString);
	var tempArr = bookMarkDataString.split("$");
	lessonLocation = tempArr[0] + "$" + tempArr[1] + "$" + tempArr[2];
	pageByPageStatus = tempArr[3];
    strCourseCompletion = Number(tempArr[4]);
	courseScore = Number(tempArr[5]);
	//alert("courseScore :: "+courseScore);

	suspendedData = pageByPageStatus + "$" + strCourseCompletion;
	//alert("set lessonLocation :: "+lessonLocation + " suspendedData:: " + suspendedData + " courseScore :: "+courseScore);
	//alert("strCourseCompletion :: "+strCourseCompletion +"courseScore :: "+courseScore);

    set_val("cmi.core.lesson_location",lessonLocation);
	set_val("cmi.suspend_data", suspendedData);

	var lmsScore = Number(get_val("cmi.core.score.raw")); //Added by sachin samel on 28-12-10

	if(courseScore < lmsScore )
	{
		courseScore = lmsScore ;
	}

	if (strCourseCompletion == 1) //Changed by sachin samel on 29-12-10
	{
		fnSendCompletion("completed");
	}
	else
	{
		fnSendCompletion("incomplete");
	}

	fnSetScore(courseScore);
}




//---------------------- Function to get bookmark data from LMS --------------------------------------//
function fnGetBookMark()
{
	
	//if(isInitiated == false) //Remove comment if course is launched in the same window
	//{
		
		//if(initSco()) //Remove comment if course is launched in the same window
	//	{
			API = GetAPI();
			
			if(API != null)
			{
				lessonLocation = get_val("cmi.core.lesson_location");
				suspendedData = get_val("cmi.suspend_data");
			}
			
			if (lessonLocation != "")
			{
				myBookMarkString = lessonLocation + "$" + suspendedData + "$" + get_val("cmi.core.score.raw");
			}else
			{
				myBookMarkString = lessonLocation;
			}

			return myBookMarkString;
		//}
	//}
}

//-------------------------- Function to get the student name from LMS --------------------------------//
function LMSStudentname()
{
	API = GetAPI();
	if( API != null )
	{	
		studentName =  API.LMSGetValue("cmi.core.student_name");
	}
	return studentName;
}

//---------------------------- Function to set the score on LMS ---------------------------------------//
function fnSetScore(score)
{
	//alert("myscore :: "+score);
   courseScore = score;
   set_val("cmi.core.score.raw", courseScore);
 
}

//---------------------------- Function is called when the course launched by LMS ---------------------//
function startTimer()
{
   logInTime = new Date().getTime();
}


//---------------------------- Function is called when the student exits the course ------------------//
function getTimeSpent()
{
		var min1;
		var sec1;

		var date = new Date();
		var logoffTime = date.getTime();
		timeDiff = logoffTime - logInTime;
		sec = timeDiff/1000;
		tmp = sec/3600;
		hrs = Math.floor(tmp);
		min1 = (tmp - hrs) * 60;
		min = Math.floor(min1);
		sec1 = (min1-min)*60;
		sec = Math.floor(sec1);
		if (hrs < 10) hrs = "0" + hrs;
		if (min < 10) min = "0" + min;
		if (sec < 10) sec = "0" + sec;
		time = hrs + ":" + min + ":" + sec;
		return time;
}


