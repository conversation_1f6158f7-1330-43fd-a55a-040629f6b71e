﻿<html>
<head>
<title>Sample</title>
<style>
	html,
	body {
		width: 100%;
		height: 100%;
		margin: 0;
		padding: 0;
		overflow: hidden;
	}
</style>
<!-- <SCRIPT LANGUAGE=javascript src="Content/CommonPages/Function.js"></Script>
<SCRIPT LANGUAGE=javascript src="Content/CommonPages/GetPlugins.js"></SCRIPT> -->
<script language="javascript" type="text/javascript">
var suspendData="";
var lessonLocation="";
function init()
{
	loadPage();
	var status = doLMSGetValue("cmi.core.lesson_status");
	alert("status "+status);
}

function capensysBodyUnload(message)
{
	suspendData = message;
}

function capensysBodyUnloadSetLocation(message)
{
	lessonLocation = message;
}

function unload()
{
	//var result = get_val("cmi.suspend_data");
	//if(result=="")
	//{
	    set_val("cmi.suspend_data",suspendData.toString());
		set_val("cmi.core.lesson_location",lessonLocation.toString());
	//}
	finish();
}

function CloseApplication()
{
	//alert("final function called");
	//window.open('','_self','');
    //window.close();
	//window.opener = self;
	//self.close();
	//window.parent.parent.parent.location.href = 'http://alpha.universitysite.com/universitysite/SelfServiceCenter.aspx';
	
	var win=window.open("about:blank","_self");
	win.close();
}

</script>
<Script language="JavaScript" src="Content/APIWrapper.js"></script>
<Script language="JavaScript" src="Content/SCOFunctions.js"></script>
<Script language="JavaScript" src="Content/scorm_api.js"></script>
</head>
<frameset rows="0,*" frameborder="NO" border="0" framespacing="0" cols="*" onload="initSco();" onunload="unload();">
<frame name="topFrame" scrolling="NO" noresize src="Content/Capensys.html" frameborder="NO" marginwidth="0" marginheight="0" >
<frame name="bottomFrame" src="Content/Capensys.html" marginwidth="0" marginheight="0" frameborder="NO">
</frameset>
<noframes>
</html>
