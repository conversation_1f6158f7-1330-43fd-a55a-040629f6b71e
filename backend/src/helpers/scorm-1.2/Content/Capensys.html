<!-- saved from url=(0014)about:internet -->
<html lang="en">

<!-- 
Smart developers always View Source. 

This application was built using Adobe Flex, an open source framework
for building rich Internet applications that get delivered via the
Flash Player or to desktops via Adobe AIR. 

Learn more about Flex at http://flex.org 
// -->

<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />

	<!--  BEGIN Browser History required section -->
	<!--<link rel="stylesheet" type="text/css" href="history/history.css" />-->
	<!--  END Browser History required section -->

	<title></title>
	<script src="AC_OETags.js" language="javascript"></script>

	<!--  BEGIN Browser History required section -->
	<!--<script src="history/history.js" language="javascript"></script>-->
	<!--  END Browser History required section -->
	<Script language="JavaScript" src="APIWrapper.js"></script>
	<Script language="JavaScript" src="SCOFunctions.js"></script>
	<Script language="JavaScript" src="scorm_api.js"></script>
	<style>
		html,
    body {
      width: 100%;
      height: 100%;
      margin: 0;
      padding: 0;
			overflow: hidden;
    }
	</style>
	<script language="JavaScript" type="text/javascript">

		function retrieveUserID() {
			var uid = get_val("cmi.core.student_id");
			document.getElementById('Capensys').setUserID(uid);
		}

		function setCompletionStatus() {
			set_val("cmi.core.lesson_status", "completed");
		}


		function closeWindow() {
			set_val("cmi.core.lesson_status", "completed");
			parent.CloseApplication();
		}

		function openUserRecommendations(message) {
			parent.location = message;
		}

		function setSuspendData(message) {
			//alert("suspend_data from flex :"+ message );
			parent.capensysBodyUnload(message.toString());
		}

		function showAlert(message) {
			alert("message from flex :" + message);
		}

		function setSuspendDataToLMS(value) {
			//alert("suspend_data from flex :"+ value );
			set_val("cmi.suspend_data", value.toString());
		}

		function chkforFirstTimeUser() {
			var result = get_val("cmi.suspend_data");
			document.getElementById('Capensys').setArrays(result);
		}

		function retrieveUserName() {
			var uname = get_val("cmi.core.student_name");
			document.getElementById('Capensys').setUserName(uname);
		}

		function setUserData(data) {
			document.getElementById('json').value = data;
		}

		function setLessonLocation(message) {
			parent.capensysBodyUnloadSetLocation(message.toString());
		}

		function retrieveLessonLocation() {
			var lloc = get_val("cmi.core.lesson_location");
			document.getElementById('Capensys').setUserLocation(lloc);
		}


	</script>
</head>

<body scroll="no">
	<form id="frmjson" method="POST">
		<input type="hidden" id="json" value="swfdata" />
	</form>
	<script>
		var count = 0;
		function checkTocloseWindow() {
			if (count === 0) {
				count++;
				return;
			}
			closeWindow();
		}
	</script>
	<iframe src="{{pathfinderUrl}}"
		style="width: 100%; height:100%;" onLoad="checkTocloseWindow()"></iframe>
	<script>
		function closeScorm() {
			set_val("cmi.core.lesson_status", "completed");
			closeWindow();
		}
		function updateLessonStatus(status) {
			set_val("cmi.core.lesson_status", status);
		}
		
		window.addEventListener('message', function(event) {
			if (event.data.event_id === 'close_scorm') {
				closeScorm();
			}
			if (event.data.event_id === 'update_lesson_status') {
				updateLessonStatus(event.data.status || "completed");
			}
		});
	</script>
</body>

</html>
