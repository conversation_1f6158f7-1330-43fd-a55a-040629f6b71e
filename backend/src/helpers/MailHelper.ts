import { TLearning } from "type";

const nodemailer = require('nodemailer');

interface IMailPayload {
  sender?: string,
  clientSlug?: string,
  to: string,
  cc?: Array<string>,
  subject: string,
  html: string,
}

class MailHelper {

  sendSMTPEmail = ({ to, cc, subject, html, sender, clientSlug }: IMailPayload) => new Promise((resolve, reject) => {
    let transport = nodemailer.createTransport({
      host: process.env.SMTP_HOST,
      port: process.env.SMTP_PORT,
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASSWORD
      }
    })
    const message: any = {
      from: `${sender || "System"} <${clientSlug || "noreply"}${process.env.SENDER_EMAIL}>`,
      to,
      subject,
      html
    };
    if (cc) message.cc = cc;
    if (process.env.DEV || process.env.CI) return resolve(true);
    transport.sendMail(message, (err, info) => {
      if (err) {
        console.log('err sendMail', err);
        return reject(err)
      } else {
        console.log(info);
        return resolve(info);
      }
    })
  })

  genLearningsTableHtml = (learnings: TLearning[]) => {
    const rows = [];
    for (let i = 0; i < learnings.length; i++) {
      const learning = learnings[i];
      rows.push(
        `<tr style="box-sizing: border-box; background-color: ${i % 2 === 0 ? 'rgb(231, 243, 244)' : 'rgb(255,255,255)'};">
          <td style="box-sizing: border-box; padding: 0.75rem; vertical-align: top; border: 2px solid white;"><img
              src="${learning.icon}"
              class="result-icon"
              width="40" height="auto"
              style="box-sizing: border-box; vertical-align: middle; border-style: none; width: 40px; height: auto;"></td>
          <td style="box-sizing: border-box; padding: 0.75rem; vertical-align: top; border: 2px solid white;">${learning.name || ''}</td>
          <td style="box-sizing: border-box; padding: 0.75rem; vertical-align: top; border: 2px solid white;"><a
              target="_blank" href="${learning.url}"
              style="box-sizing: border-box; color: blue; text-decoration-line: none;">${learning.urlShortName || learning.url || ''}</a></td>
          <td style="box-sizing: border-box; padding: 0.75rem; vertical-align: top; border: 2px solid white;">${learning.comment || ''}</td>
        </tr>`
      )
    }

    const html = `
    <table class="table"
      style="box-sizing: border-box; border-collapse: collapse; width: 1068px; max-width: 100%; margin-bottom: 1rem; color: rgb(33, 37, 41); font-family: Arial, Helvetica, sans-serif; font-size: 14px;">
      <thead style="box-sizing: border-box;">
        <tr style="box-sizing: border-box;">
          <th scope="col"
            style="box-sizing: border-box; padding: 0.75rem; vertical-align: bottom; border: 2px solid white; color: white; background-color: rgb(113, 191, 196);">
            Type</th>
          <th scope="col"
            style="box-sizing: border-box; padding: 0.75rem; vertical-align: bottom; border: 2px solid white; color: white; background-color: rgb(113, 191, 196);">
            Learning Name</th>
          <th scope="col"
            style="box-sizing: border-box; padding: 0.75rem; vertical-align: bottom; border: 2px solid white; color: white; background-color: rgb(113, 191, 196);">
            URL</th>
          <th scope="col"
            style="box-sizing: border-box; padding: 0.75rem; vertical-align: bottom; border: 2px solid white; color: white; background-color: rgb(113, 191, 196);">
            Comment</th>
        </tr>
      </thead>
      <tbody style="box-sizing: border-box;">
        ${rows.join('\n')}
      </tbody>
    </table>
    `
    return html;
  }

  genChangePasswordEmail = ({ firstName, lastName, password, link }) => {
    return `
      <p>Hi <b>${firstName} ${lastName}</b></p>
      <p>Your password has changed to ${password}. Please ignore this message if you requested a new password.</p>
      <p>In case you did not, please go to this <strong><a href="${link}">link</a></strong> to reset your password</p>
      <p>Harbor</p>
    `
  }

  genForgotPasswordEmail = ({ firstName, lastName, link }) => {
    return `
      <p>Hi <b>${firstName} ${lastName}</b></p>
      <p>You have requested a password reset. Please click here to reset your password:</p>
      <a target="_blank" href="${link}">${link}</a>
      <p>If you did not request a password reset, please contact your Pathfinder Administrator.</p>
      <p>Harbor</p>
    `
  }
}

export default new MailHelper()
