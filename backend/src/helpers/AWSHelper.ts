const AWS = require('aws-sdk');
const fs = require('fs');
const mime = require('mime');
const axios = require('axios');

const USING_AWS = false;

class AWSHelper {
  constructor() {
    this.init();
  }

  s3Info = !USING_AWS ? {
    access_key: process.env.DO_ACCESS_KEY_ID?.trim(),
    access_secret: process.env.DO_SECRET_ACCESS_KEY?.trim(),
    bucket: process.env.DO_S3_BUCKET?.trim(),
    region: process.env.DO_REGION?.trim(),
  } : {
    access_key: process.env.AWS_ACCESS_KEY_ID?.trim(),
    access_secret: process.env.AWS_SECRET_ACCESS_KEY?.trim(),
    bucket: process.env.AWS_S3_BUCKET?.trim(),
    region: process.env.AWS_REGION?.trim()
  };
  s3

  init() {
    if (USING_AWS) {
      AWS.config.update({
        region: this.s3Info.region,
        accessKeyId: this.s3Info.access_key,
        secretAccessKey: this.s3Info.access_secret,
      });
    } else {
      AWS.config.update({
        accessKeyId: this.s3Info.access_key,
        secretAccessKey: this.s3Info.access_secret,
        endpoint: new AWS.Endpoint(`${this.s3Info.region}.digitaloceanspaces.com`),
        signatureVersion: 'v4',
      });
    }
    this.s3 = new AWS.S3();
  }

  getSignedUrl(key) {
    const url = this.s3.getSignedUrl('getObject', {
      Bucket: this.s3Info.bucket,
      Key: key,
      Expires: 60 * 15,
    })
    return url;
  }

  uploadedDomain = !USING_AWS ? `https://${this.s3Info.bucket}.${this.s3Info.region}.digitaloceanspaces.com` : `https://${this.s3Info.bucket}.s3.eu-west-1.amazonaws.com`;

  upload = async ({ filePath, key, isPrivate = false }, shouldLog = false) => {
    const objectParams = {
      ACL: isPrivate ? 'private' : 'public-read',
      Bucket: this.s3Info.bucket,
      Key: key,
      Body: fs.createReadStream(filePath),
      ContentType: mime.getType(filePath),
    };
    const res = await this.s3.putObject(objectParams).promise();
    if (shouldLog) console.log(res);
    return `${this.uploadedDomain}/${key}`;
  }

  uploadFileFromURL = async ({ url, key, isPrivate = false }, shouldLog = false) => {
    try {
      const response = await axios.get(url, { responseType: 'arraybuffer' });
      const fileBuffer = Buffer.from(response.data, 'binary');
      const fileType = mime.getExtension(response.headers['content-type']);
      const contentType = ['jpeg', 'png', 'webp', 'gif'].includes(fileType)
        ? `image/${fileType}`
        : 'application/octet-stream'

      const objectParams = {
        ACL: isPrivate ? 'private' : 'public-read',
        Bucket: this.s3Info.bucket,
        Key: key,
        Body: fileBuffer,
        ContentType: contentType,
      };

      const res = await this.s3.putObject(objectParams).promise();
      if (shouldLog) console.log(res);
      return `${this.uploadedDomain}/${key}`;
    } catch (error) {
      return '';
    }
  }

  getSignedUrlForUpload(key, contentType) {
    const url = this.s3.getSignedUrl('putObject', {
      Bucket: this.s3Info.bucket,
      Key: key,
      Expires: 60 * 15,
      ACL: 'public-read',
      ContentType: contentType,
    })
    return url;
  }
}

export default new AWSHelper();
