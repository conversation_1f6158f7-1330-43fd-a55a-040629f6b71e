var CronJob = require('cron').CronJob;
const { exec } = require('child_process');
const fs = require('fs');
import { DB } from 'db';
import * as moment from 'moment-timezone';
import { AWSHelper } from 'helpers';

const dbContainerName = `pathfinder-db`;

const execCommand = (command: string, env: any) => new Promise((resolve, reject) => {
  exec(command, { env }, (error) => {
    if (error) {
      reject(error)
    } else {
      resolve(true)
    }
  })
})

export const execute = async () => {
  try {
    const fileName = `db-backup-${moment().format('YYYYMMDD')}.tar`;
    const { database, username, password, host, port } = DB.instance.config;
    const env = Object.assign({}, process.env, { PGPASSWORD: password });
    const command = `docker exec ${dbContainerName} pg_dump -h ${host} -p 5432 -U ${username} -d ${database} -f ${fileName} -F t`;
    const copyCommand = `docker cp ${dbContainerName}:${fileName} ${fileName}`;

    await execCommand(command, env);
    await execCommand(copyCommand, env);

    const url = await AWSHelper.upload({
      filePath: fileName,
      key: `db-backup/${moment().format('YYYYMMDD')}.tar`,
      isPrivate: true,
    });

    console.log('Backup db success:', url);
    fs.unlinkSync(fileName);
  } catch (error) {
    console.log('Backup db failed:', error);
  }
};

export const backupDatabaseEveryDay = () => {
  if (process.env.DEV) return;
  var job = new CronJob('00 00 00 * * *', function () {
    console.log('backupDatabaseEveryDay');
    execute();
  }, null, false, 'Europe/London');
  job.start();
  execute();
};
