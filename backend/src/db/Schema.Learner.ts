import { BuildOptions, Model, DataTypes, Sequelize } from "sequelize";
import { stringifyDataType } from "./Utils.Schema";
import { TLearner } from 'type';

export type LearnerSchema = Partial<TLearner>;

// For Typescript type stuff
export interface LearnerModel extends Model<LearnerSchema>, LearnerSchema {}
export type LearnerStatic = typeof Model & {
  new (values?: object, options?: BuildOptions): LearnerModel;
};

export const tableDefine = {
  name: "learners",
  columns: {
    id: {
      type: DataTypes.TEXT,
      primaryKey: true,
    },
    email: {
      type: DataTypes.TEXT,
    },
    firstName: {
      type: DataTypes.TEXT,
    },
    lastName: {
      type: DataTypes.TEXT,
    },
    deletedAt: {
      type: DataTypes.DATE,
    },
    data: {
      type: DataTypes.TEXT,
      ...stringifyDataType('data'),
    }
  }
}

export const createLearner = async (instance: Sequelize): Promise<LearnerStatic> => {
  const Learner = <LearnerStatic>instance.define(
    tableDefine.name,
    tableDefine.columns,
    {
      indexes: [],
      paranoid: true,
    }
  );

  Learner.beforeSave((Learner: LearnerModel, options) => {});

  await Learner.sync({ force: false });
  return <PERSON>rner;
};
