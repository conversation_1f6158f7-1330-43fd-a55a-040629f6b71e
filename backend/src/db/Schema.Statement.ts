import { BuildOptions, Model, DataTypes, Sequelize } from "sequelize";
import { TStatement } from 'type';
import { stringifyDataType } from "./Utils.Schema";

export type StatementSchema = Partial<TStatement>;

// For Typescript type stuff
export interface StatementModel extends Model<StatementSchema>, StatementSchema {}
export type StatementStatic = typeof Model & {
  new (values?: object, options?: BuildOptions): StatementModel;
};

export const tableDefine = {
  name: "statements",
  columns: {
    id: {
      type: DataTypes.TEXT,
      primaryKey: true,
    },
    clientId: {
      type: DataTypes.TEXT,
    },
    pathfinderId: {
      type: DataTypes.TEXT,
    },
    orderIndex: {
      type: DataTypes.INTEGER,
    },
    statement: {
      type: DataTypes.TEXT,
    },
    rolloverLikert1: {
      type: DataTypes.TEXT,
    },
    rolloverLikert2: {
      type: DataTypes.TEXT,
    },
    rolloverLikert3: {
      type: DataTypes.TEXT,
    },
    elementIds: {
      type: DataTypes.TEXT,
    },
    learningsLikert1: {
      type: DataTypes.ARRAY(DataTypes.JSON),
    },
    learningsLikert2: {
      type: DataTypes.ARRAY(DataTypes.JSON),
    },
    learningsLikert3: {
      type: DataTypes.ARRAY(DataTypes.JSON),
    },
    learningIdsLikert1: {
      type: DataTypes.TEXT,
    },
    learningIdsLikert2: {
      type: DataTypes.TEXT,
    },
    learningIdsLikert3: {
      type: DataTypes.TEXT,
    },
    deletedAt: {
      type: DataTypes.DATE,
    },
    data: {
      type: DataTypes.TEXT,
      ...stringifyDataType('data'),
    }
  }
}

export const createStatement = async (instance: Sequelize): Promise<StatementStatic> => {
  const Statement = <StatementStatic>instance.define(
    tableDefine.name,
    tableDefine.columns,
    {
      indexes: [],
      paranoid: true,
    }
  );

  Statement.beforeSave((Statement: StatementModel, options) => {});

  await Statement.sync({ force: false });
  return Statement;
};
