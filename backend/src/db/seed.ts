import { <PERSON>ar<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'helpers';
import { TLearning } from 'type';
import dbInstace from './DB.Postgres';
import { kebabCase } from 'lodash';

export const seed = async (DB: typeof dbInstace) => {

  const flushDB = async () => {
    if (process.env.FLUSH_ON_START) {
      await DB.Pathfinder.destroy({ truncate: true, force: true })
      await DB.Element.destroy({ truncate: true, force: true })
      await DB.Statement.destroy({ truncate: true, force: true })
      await DB.Learning.destroy({ truncate: true, force: true })
      await DB.User.destroy({ truncate: true, force: true })
      await DB.Client.destroy({ truncate: true, force: true })
      console.log('FLUSH DB COMPLETED')
    }
  }

  const createIfNotExistAdmin = async () => {
    const admin = await DB.User.findOne({
      where: {
        email: '<EMAIL>',
      }
    })
    if (!admin) {
      const hashedPassword = await AuthenHelper.hashPassword('CD%2023');
      await DB.User.create({
        id: VarHelper.genId(),
        firstName: 'Admin',
        lastName: 'CD',
        email: '<EMAIL>',
        role: 'admin',
        password: hashedPassword as string,
        multiFactorType: ['NONE'],
        phoneNumber: null
      })
    }

  }

  const createIfNotExistClient = async () => {
    let client = await DB.Client.findOne({
      where: {
        slug: 'demo-client',
      }
    });
    if (!client) {
      const clientId = VarHelper.genId()
      await DB.Client.create({
        id: clientId,
        name: 'Demo Client',
        slug: 'demo-client',
        region: global.region,
      });
      const hashedPassword = await AuthenHelper.hashPassword('CD%2023');
      await DB.User.create({
        id: VarHelper.genId(),
        firstName: 'Demo Client',
        lastName: 'Admin',
        email: '<EMAIL>',
        multiFactorType: ['NONE'],
        password: hashedPassword as string,
        clientId,
        role: 'client',
      });
    } else {
      const clientUser = await DB.User.findOne({
        where: {
          clientId: client.id,
          role: 'client',
        },
      });
      if (!clientUser) {
        const hashedPassword = await AuthenHelper.hashPassword('CD%2023');
        try {
          await DB.User.create({
            id: VarHelper.genId(),
            firstName: 'Demo Client',
            lastName: 'Admin',
            email: '<EMAIL>',
            multiFactorType: ['NONE'],
            password: hashedPassword as string,
            clientId: client.id,
            role: 'client',
          });
        } catch (error) { }
      }
    }
  }

  const updateExistedClients = async () => {
    if (!global.isMainServer) return;
    const clients = await DB.Client.findAll();
    await Promise.all(clients.map(async client => {
      if (!client.region) {
        client.region = global.region;
        client.slug = kebabCase(client.name);
        await client.save();
        console.log("seed-data: update client", client.toJSON());
      }
      return;
    }))
  }

  const createDemoPathfinder = async () => {
    try {
      let _pathfinder = await DB.Pathfinder.findOne({
        where: {
          id: 'pathfinder0'
        }
      });
      const client = await DB.Client.findOne({
        where: {
          slug: 'demo-client',
        }
      });
      if (!client) return;
      if (!_pathfinder) {
        _pathfinder = await DB.Pathfinder.create({
          id: 'pathfinder0',
          clientId: client.id,
          name: 'Work 10',
          welcomeInstruction: 'Select your role to find course in less than 2 minutes!',
          likertScaleTitle1: 'Never',
          likertScaleTitle2: 'Sometimes',
          likertScaleTitle3: 'Always',
          logo: 'https://pathfinder-cd.fra1.digitaloceanspaces.com/files/1692091962204-course-icon.png',
        })
      }
      const existedElement = await DB.Element.findByPk("pathfinder0_0");
      if (!existedElement) {
        const MOCK_ELEMENTS = [
          { name: 'Author', rolloverText: 'Create documents fron scratch; researches', filterKeys: 'Chicago, New York', image: 'https://pathfinder-cd.fra1.digitaloceanspaces.com/files/1692091962204-course-icon.png' },
          { name: 'Matter manager', rolloverText: 'Curales the workspaces; knows DMS well', filterKeys: 'Chicago, Corporate' },
          { name: 'Mobile Lawyer', rolloverText: 'Remote access to the DMS', filterKeys: 'Corporate' },
          { name: 'Reviewer', rolloverText: 'Does not create documents; reviews, make comments', filterKeys: 'New York' },
        ]
        await Promise.all(
          MOCK_ELEMENTS.map((i, idx) => DB.Element.create({
            id: `pathfinder0_${idx}`,
            orderIndex: idx,
            pathfinderId: _pathfinder.id,
            name: i.name,
            rolloverText: i.rolloverText,
            image: i.image,
            filterKeys: i.filterKeys,
          }))
        )
      }

      const existedLearning = await DB.Learning.findByPk("learning0");
      if (!existedLearning) {
        const MOCK_LEARNINGS = [
          { name: '356O - Working with PDFs', icon: 'https://pathfinder-cd.fra1.digitaloceanspaces.com/files/1692091962204-course-icon.png', tags: '356,PDF' },
          { name: '365 Advanced  Workshop', icon: 'https://pathfinder-cd.fra1.digitaloceanspaces.com/files/1692091962204-course-icon.png', tags: '365,Advance' },
          { name: '365N - Collaborating on Documents', icon: 'https://pathfinder-cd.fra1.digitaloceanspaces.com/files/1692091962204-course-icon.png', tags: '365,Doc' },
          { name: '365N - Preparing PowerPoint Presentations', icon: 'https://pathfinder-cd.fra1.digitaloceanspaces.com/files/1692091962204-course-icon.png', tags: '356,Powerpoint' },
        ]

        // @ts-ignore
        const mockedLearnings: TLearning[] = await Promise.all(
          MOCK_LEARNINGS.map((i, idx) => DB.Learning.create({
            id: `learning${idx}`,
            clientId: client.id,
            name: i.name,
            icon: i.icon,
            url: 'https://www.capensyslearnwise.com/catalog/courses/',
            tags: i.tags,
          }))
        )
        const MOCK_STATEMENTS = [
          {
            statement: 'I create multiple versions of document and need to open earlier versions from NetDocuments, as well as from within Word.',
            rollover1: 'I never do this and dont need training.',
            rollover2: 'Searching for Documents from Word Using the iManage Work Add-in',
            rollover3: 'Opening Document from Word Using the iManage Work Add In',
            elements: 'pathfinder0_0,pathfinder0_1,pathfinder0_2,pathfinder0_3',
          },
          {
            statement: 'I create brand new documents and save them to client-related NetDocuments workspaces. I also save copies of documents as new documents or as a new versions of the original document.',
            rollover1: 'I never do this and dont need training.',
            rollover2: 'Saving Copy as New Document',
            rollover3: 'Saving as New Version',
            elements: 'pathfinder0_0,pathfinder0_1,pathfinder0_2,pathfinder0_3',
          },
          {
            statement: 'I want to use NetDocuments as effectively as possible, and would like to understand how the Firm has structured it',
            rollover1: 'I never do this and dont need training.',
            rollover2: 'Searching for Documents from Word Using the iManage Work Add-in',
            rollover3: 'Opening Document from Word Using the iManage Work Add In',
            elements: 'pathfinder0_0,pathfinder0_1,pathfinder0_2,pathfinder0_3',
          },
          {
            statement: 'If I save a document or e-mail under the wrong client-matter, I want to be able to move it to the proper location.',
            rollover1: 'I never do this and dont need training.',
            rollover2: 'Searching for Documents from Word Using the iManage Work Add-in',
            rollover3: 'Opening Document from Word Using the iManage Work Add In',
            elements: 'pathfinder0_0,pathfinder0_1,pathfinder0_2,pathfinder0_3',
          },
        ]
        await Promise.all(
          MOCK_STATEMENTS.map((i, idx) => DB.Statement.create({
            id: `statement${idx}`,
            orderIndex: idx,
            pathfinderId: _pathfinder.id,
            statement: i.statement,
            rolloverLikert1: i.rollover1,
            rolloverLikert2: i.rollover2,
            rolloverLikert3: i.rollover3,
            elementIds: i.elements,
            learningIdsLikert1: 'learning0,learning1',
            learningIdsLikert2: 'learning1,learning2',
            learningIdsLikert3: 'learning2,learning3',
            learningsLikert1: [mockedLearnings[0], mockedLearnings[1]],
            learningsLikert2: [mockedLearnings[1], mockedLearnings[2]],
            learningsLikert3: [mockedLearnings[2], mockedLearnings[3]],
          }))
        )
      }

    } catch (error) {
      console.log('xxx', error);
    }
  }

  await flushDB();

  await updateExistedClients();

  await Promise.all([
    createIfNotExistAdmin(),
    createIfNotExistClient(),
  ])

  await createDemoPathfinder();
}
