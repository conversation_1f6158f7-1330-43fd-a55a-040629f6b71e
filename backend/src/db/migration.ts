import dbInstace from './DB.Postgres';
import * as UserSchema from './Schema.User';
import * as GeneralDataSchema from './Schema.GeneralData';
import * as ClientSchema from './Schema.Client';
import * as PathfinderSchema from './Schema.Pathfinder';
import * as ElementSchema from './Schema.Element';
import * as StatementSchema from './Schema.Statement';
import * as LearningSchema from './Schema.Learning';
import * as LearnerSchema from './Schema.Learner';
import * as LearnerLikertSchema from './Schema.LearnerLikerts';
import * as ImageSchema from './Schema.Image';

const ALL_SCHEMAS = [
  UserSchema, GeneralDataSchema, ClientSchema, PathfinderSchema,
  ElementSchema, StatementSchema, LearningSchema, LearnerSchema,
  LearnerLikertSchema, ImageSchema,
];

export const migration = async (DB: typeof dbInstace) => {
  await Promise.all(
    ALL_SCHEMAS.map(async (schema) => {
      const tableName = schema.tableDefine.name;
      const tableDefinition = await DB.instance.queryInterface.describeTable(tableName);
      await Promise.all(
        Object.keys(schema.tableDefine.columns).map(async (colName) => {
          if (!tableDefinition[colName]) {
            const { type, defaultValue } = schema.tableDefine.columns[colName]
            await DB.instance.queryInterface.addColumn(tableName, colName, {
              type,
              defaultValue: defaultValue || null,
            });
            console.log(`Auto migrate: Add column ${tableName}.${colName}`)
          }
        })
      )
    })
  )
}
