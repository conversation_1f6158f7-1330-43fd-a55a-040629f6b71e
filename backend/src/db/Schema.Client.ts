import { BuildOptions, Model, DataTypes, Sequelize } from "sequelize";
import { TClient } from 'type';
import { sendToSubDB } from "api/api-middlewares/syncMultiDB";
import { stringifyDataType } from "./Utils.Schema";

export type ClientSchema = Partial<TClient>;

// For Typescript type stuff
export interface ClientModel extends Model<ClientSchema>, ClientSchema {}
export type ClientStatic = typeof Model & {
  new (values?: object, options?: BuildOptions): ClientModel;
};

export const tableDefine = {
  name: "clients",
  columns: {
    id: {
      type: DataTypes.TEXT,
      primaryKey: true,
    },
    name: {
      type: DataTypes.TEXT,
    },
    logo: {
      type: DataTypes.TEXT,
    },
    location: {
      type: DataTypes.TEXT,
    },
    slug: {
      type: DataTypes.TEXT,
    },
    region: {
      type: DataTypes.TEXT,
    },
    deletedAt: {
      type: DataTypes.DATE,
    },
    data: {
      type: DataTypes.TEXT,
      ...stringifyDataType('data'),
    }
  }
}

export const createClient = async (instance: Sequelize): Promise<ClientStatic> => {
  const Client = <ClientStatic>instance.define(
    tableDefine.name,
    tableDefine.columns,
    {
      indexes: [],
      paranoid: true,
    }
  );

  Client.beforeSave((Client: ClientModel, options) => {});

  Client.afterUpdate((client) => {
    sendToSubDB("/api/clients/sync", client.toJSON());
  })

  Client.afterCreate((client) => {
    sendToSubDB("/api/clients/sync", client.toJSON());
  })

  await Client.sync({ force: false });
  return Client;
};
