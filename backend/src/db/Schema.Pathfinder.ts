import { BuildOptions, Model, DataTypes, Sequelize } from "sequelize";
import { TPathfinder } from 'type';
import { stringifyDataType } from "./Utils.Schema";

export type PathfinderSchema = Partial<TPathfinder>;

// For Typescript type stuff
export interface PathfinderModel extends Model<PathfinderSchema>, PathfinderSchema {}
export type PathfinderStatic = typeof Model & {
  new (values?: object, options?: BuildOptions): PathfinderModel;
};

export const tableDefine = {
  name: "pathfinders",
  columns: {
    id: {
      type: DataTypes.TEXT,
      primaryKey: true,
    },
    clientId: {
      type: DataTypes.TEXT,
    },
    orderIndex: {
      type: DataTypes.INTEGER,
    },
    name: {
      type: DataTypes.TEXT,
    },
    logo: {
      type: DataTypes.TEXT,
    },
    url: {
      type: DataTypes.DATE,
    },
    deletedAt: {
      type: DataTypes.DATE,
    },
    welcomeInstruction: {
      type: DataTypes.TEXT,
    },
    additionalInstruction: {
      type: DataTypes.TEXT,
    },
    completionInstruction: {
      type: DataTypes.TEXT,
    },
    listStatementTitle: {
      type: DataTypes.TEXT,
    },
    listStatementSubtitle: {
      type: DataTypes.TEXT,
    },
    listLearningTitle: {
      type: DataTypes.TEXT,
    },
    likertScaleTitle1: {
      type: DataTypes.TEXT,
    },
    likertScaleTitle2: {
      type: DataTypes.TEXT,
    },
    likertScaleTitle3: {
      type: DataTypes.TEXT,
    },
    elementsTitle: {
      type: DataTypes.TEXT,
    },
    disableElementsFilter: {
      type: DataTypes.BOOLEAN,
    },
    sendEmail: {
      type: DataTypes.BOOLEAN,
    },
    hidePathfinderLogo: {
      type: DataTypes.BOOLEAN,
    },
    additionalRecipients: {
      type: DataTypes.TEXT,
    },
    emailSubject: {
      type: DataTypes.TEXT,
    },
    emailContent: {
      type: DataTypes.TEXT,
    },
    emailFooter: {
      type: DataTypes.TEXT,
    },
    data: {
      type: DataTypes.TEXT,
      ...stringifyDataType('data'),
    }
  }
}

export const createPathfinder = async (instance: Sequelize): Promise<PathfinderStatic> => {
  const Pathfinder = <PathfinderStatic>instance.define(
    tableDefine.name,
    tableDefine.columns,
    {
      indexes: [],
      paranoid: true,
    }
  );

  Pathfinder.beforeSave((Pathfinder: PathfinderModel, options) => {});

  await Pathfinder.sync({ force: false });
  return Pathfinder;
};
