import { BuildOptions, Model, DataTypes, Sequelize } from "sequelize";
import { stringifyDataType } from "./Utils.Schema";
import { TUser } from 'type';
import { sendToSubDB } from "api/api-middlewares/syncMultiDB";

export type UserSchema = Partial<TUser>;

// For Typescript type stuff
export interface UserModel extends Model<UserSchema>, UserSchema {}
export type UserStatic = typeof Model & {
  new (values?: object, options?: BuildOptions): UserModel;
};

export const tableDefine = {
  name: "users",
  columns: {
    id: {
      type: DataTypes.TEXT,
      primaryKey: true,
    },
    clientId: {
      type: DataTypes.TEXT,
    },
    email: {
      type: DataTypes.TEXT,
      unique: true,
    },
    role: {
      type: DataTypes.TEXT,
    },
    firstName: {
      type: DataTypes.TEXT,
    },
    lastName: {
      type: DataTypes.TEXT,
    },
    password: {
      type: DataTypes.TEXT,
    },
    resetPasswordCode: {
      type: DataTypes.TEXT,
    },
    deletedAt: {
      type: DataTypes.DATE,
    },
    data: {
      type: DataTypes.TEXT,
      ...stringifyDataType('data'),
    },
    multiFactorType: {
      type: DataTypes.ARRAY(DataTypes.TEXT),
    },
    phoneNumber: {
      type: DataTypes.TEXT,
    },
    secret: {
      type: DataTypes.TEXT,
    },
  }
}

export const createUser = async (instance: Sequelize): Promise<UserStatic> => {
  const User = <UserStatic>instance.define(
    tableDefine.name,
    tableDefine.columns,
    {
      indexes: [],
      paranoid: true,
    }
  );

  User.beforeSave((User: UserModel, options) => {});

  User.afterUpdate((user) => {
    sendToSubDB("/api/users/sync", user.toJSON());
  })

  User.afterCreate((user) => {
    sendToSubDB("/api/users/sync", user.toJSON());
  })

  await User.sync({ force: false });
  return User;
};
