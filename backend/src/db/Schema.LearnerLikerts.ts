import { BuildOptions, Model, DataTypes, Sequelize } from "sequelize";
import { stringifyDataType } from "./Utils.Schema";
import { TLearnerLikerts } from 'type';

export type LearnerLikertsSchema = Partial<TLearnerLikerts>;

// For Typescript type stuff
export interface LearnerLikertsModel extends Model<LearnerLikertsSchema>, LearnerLikertsSchema { }
export type LearnerLikertsStatic = typeof Model & {
  new(values?: object, options?: BuildOptions): LearnerLikertsModel;
};

export const tableDefine = {
  name: "learner_likerts",
  columns: {
    id: {
      type: DataTypes.TEXT,
      primaryKey: true,
    },
    learnerId: {
      type: DataTypes.TEXT,
    },
    clientId: {
      type: DataTypes.TEXT,
    },
    elementId: {
      type: DataTypes.TEXT,
    },
    pathfinderId: {
      type: DataTypes.TEXT,
    },
    pathfinderName: {
      type: DataTypes.TEXT,
    },
    likerts: {
      type: DataTypes.JSON,
    },
    data: {
      type: DataTypes.TEXT,
    },
    deletedAt: {
      type: DataTypes.DATE,
    },
  }
}

export const createLearnerLikerts = async (instance: Sequelize): Promise<LearnerLikertsStatic> => {
  const LearnerLikerts = <LearnerLikertsStatic>instance.define(
    tableDefine.name,
    tableDefine.columns,
    {
      indexes: [],
      paranoid: true,
    }
  );

  LearnerLikerts.beforeSave((LearnerLikerts: LearnerLikertsModel, options) => { });

  await LearnerLikerts.sync({ force: false });
  return LearnerLikerts;
};
