import { BuildOptions, Model, DataTypes, Sequelize } from "sequelize";
import { stringifyDataType } from "./Utils.Schema";
import { TImage } from 'type';

export type ImageSchema = Partial<TImage>;

// For Typescript type stuff
export interface ImageModel extends Model<ImageSchema>, ImageSchema { }
export type ImageStatic = typeof Model & {
  new(values?: object, options?: BuildOptions): ImageModel;
};

export const tableDefine = {
  name: "images",
  columns: {
    id: {
      type: DataTypes.TEXT,
      primaryKey: true,
    },
    clientId: {
      type: DataTypes.TEXT,
    },
    url: {
      type: DataTypes.TEXT,
    },
    name: {
      type: DataTypes.TEXT,
    },
    type: {
      type: DataTypes.TEXT,
    },
    data: {
      type: DataTypes.TEXT,
    },
    deletedAt: {
      type: DataTypes.DATE,
    },
  }
}

export const createImage = async (instance: Sequelize): Promise<ImageStatic> => {
  const Image = <ImageStatic>instance.define(
    tableDefine.name,
    tableDefine.columns,
    {
      indexes: [],
      paranoid: true,
    }
  );

  Image.beforeSave((Image: ImageModel, options) => { });

  await Image.sync({ force: false });
  return Image;
};
