import { BuildOptions, Model, DataTypes, Sequelize, Op } from "sequelize";
import { TLearning } from 'type';
import { stringifyDataType } from "./Utils.Schema";
import { StatementStatic } from "./Schema.Statement";

export type LearningSchema = Partial<TLearning>;

// For Typescript type stuff
export interface LearningModel extends Model<LearningSchema>, LearningSchema { }
export type LearningStatic = typeof Model & {
  new(values?: object, options?: BuildOptions): LearningModel;
};

export const tableDefine = {
  name: "learnings",
  columns: {
    id: {
      type: DataTypes.TEXT,
      primaryKey: true,
    },
    clientId: {
      type: DataTypes.TEXT,
    },
    name: {
      type: DataTypes.TEXT,
    },
    icon: {
      type: DataTypes.TEXT,
    },
    type: {
      type: DataTypes.TEXT,
    },
    url: {
      type: DataTypes.TEXT,
    },
    urlShortName: {
      type: DataTypes.TEXT,
    },
    comment: {
      type: DataTypes.TEXT,
    },
    tags: {
      type: DataTypes.TEXT,
    },
    deletedAt: {
      type: DataTypes.DATE,
    },
    data: {
      type: DataTypes.TEXT,
      ...stringifyDataType('data'),
    }
  }
}

export const createLearning = async (instance: Sequelize, Statement: StatementStatic): Promise<LearningStatic> => {
  const Learning = <LearningStatic>instance.define(
    tableDefine.name,
    tableDefine.columns,
    {
      indexes: [],
      paranoid: true,
    }
  );

  Learning.beforeSave((Learning: LearningModel, options) => { });
  Learning.beforeUpdate(async (learning) => {
    const statements = await Statement.findAll({
      where: {
        [Op.or]: [
          { learningIdsLikert1: { [Op.like]: `%${learning.id}%` } },
          { learningIdsLikert2: { [Op.like]: `%${learning.id}%` } },
          { learningIdsLikert3: { [Op.like]: `%${learning.id}%` } },
        ]
      }
    });
    statements.forEach(async (item) => {
      const updateData: any = {};
      if (item.learningIdsLikert1?.includes(learning.id)) {
        const targetIdx = item.learningsLikert1.findIndex((i) => i.id === learning.id);
        const arr = [...item.learningsLikert1];
        arr[targetIdx] = learning.toJSON() as TLearning;
        updateData.learningsLikert1 = arr;
      }
      if (item.learningIdsLikert2?.includes(learning.id)) {
        const targetIdx = item.learningsLikert2.findIndex((i) => i.id === learning.id);
        const arr = [...item.learningsLikert2];
        arr[targetIdx] = learning.toJSON() as TLearning;
        updateData.learningsLikert2 = arr;
      }
      if (item.learningIdsLikert3?.includes(learning.id)) {
        const targetIdx = item.learningsLikert3.findIndex((i) => i.id === learning.id);
        const arr = [...item.learningsLikert3];
        arr[targetIdx] = learning.toJSON() as TLearning;
        updateData.learningsLikert3 = arr;
      }
      await item.update(updateData);
    })
  });

  await Learning.sync({ force: false });
  return Learning;
};
