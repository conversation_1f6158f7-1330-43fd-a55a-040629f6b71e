import { BuildOptions, Model, DataTypes, Sequelize } from "sequelize";
import { TElement } from 'type';
import { stringifyDataType } from "./Utils.Schema";

export type ElementSchema = Partial<TElement>;

// For Typescript type stuff
export interface ElementModel extends Model<ElementSchema>, ElementSchema {}
export type ElementStatic = typeof Model & {
  new (values?: object, options?: BuildOptions): ElementModel;
};

export const tableDefine = {
  name: "elements",
  columns: {
    id: {
      type: DataTypes.TEXT,
      primaryKey: true,
    },
    clientId: {
      type: DataTypes.TEXT,
    },
    pathfinderId: {
      type: DataTypes.TEXT,
    },
    orderIndex: {
      type: DataTypes.INTEGER,
    },
    name: {
      type: DataTypes.TEXT,
    },
    rolloverText: {
      type: DataTypes.TEXT,
    },
    image: {
      type: DataTypes.TEXT,
    },
    filterKeys: {
      type: DataTypes.TEXT,
    },
    deletedAt: {
      type: DataTypes.DATE,
    },
    data: {
      type: DataTypes.TEXT,
      ...stringifyDataType('data'),
    }
  }
}

export const createElement = async (instance: Sequelize): Promise<ElementStatic> => {
  const Element = <ElementStatic>instance.define(
    tableDefine.name,
    tableDefine.columns,
    {
      indexes: [],
      paranoid: true,
    }
  );

  Element.beforeSave((Element: ElementModel, options) => {});

  await Element.sync({ force: false });
  return Element;
};
