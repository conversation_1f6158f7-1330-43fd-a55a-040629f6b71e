import { QueryInterface } from 'sequelize';
const Sequelize = require('sequelize');
export const Op = Sequelize.Op;
const GlobalEvent = require('js-events-listener');

const { Client } = require("pg");
import { UserStatic, createUser } from './Schema.User';
import { GeneralDataStatic, createGeneralData } from './Schema.GeneralData';
import { ClientStatic, createClient } from './Schema.Client';
import { PathfinderStatic, createPathfinder } from './Schema.Pathfinder';
import { ElementStatic, createElement } from './Schema.Element';
import { StatementStatic, createStatement } from './Schema.Statement';
import { LearningStatic, createLearning } from './Schema.Learning';
import { seed } from './seed';
import { migration } from './migration';
import { LearnerStatic, createLearner } from './Schema.Learner';
import { LearnerLikertsStatic, createLearnerLikerts } from './Schema.LearnerLikerts';
import { ImageStatic, createImage } from './Schema.Image';

interface IOtherConfig {
  database?: string,
  username?: string,
  password?: string,
  host?: string,
  port?: string,
}

class DB {

  instance;
  client;
  randomID = '';

  _ready = false;
  makeReady = () => {
    this._ready = true;
    GlobalEvent.emit('DB_READY', undefined);
  }
  onReady = () => new Promise((resolve, reject) => {
    if (this._ready) return resolve(undefined);
    GlobalEvent.on('DB_READY', () => {
      resolve(undefined);
    });
  })

  async init(otherConfig : IOtherConfig = {}) {

    if (this.instance) return this.instance;
    const DB_CONNECTION_STRING = process.env.DEV ? process.env.DB_CONNECTION_STRING_DEV : process.env.DB_CONNECTION_STRING_PROD;
    // console.log('DB_CONNECTION_STRING', DB_CONNECTION_STRING);
    this.instance = new Sequelize(`${DB_CONNECTION_STRING}?sslmode=require`, {
      logging: false,
    });
    await this.migrateTables();
    return this.instance;
  }

  async initTest(otherConfig : IOtherConfig = {}) {
    if (this.instance) return this.instance;

    const client = new Client({
      user: process.env.POSTGRES_USER,
      password: process.env.POSTGRES_PASSWORD,
      host: process.env.POSTGRES_HOST,
      database: "postgres",
      port: process.env.POSTGRES_PORT,
    });
    this.client = client;
    console.log({
      user: process.env.POSTGRES_USER,
      password: process.env.POSTGRES_PASSWORD,
      host: process.env.POSTGRES_HOST,
      database: "postgres",
      port: process.env.POSTGRES_PORT,
    });
    await client.connect();
    const timestamp = String(new Date().getTime())
    const randomID = String(Math.random()).replace(".", '').slice(0, 4) + timestamp.slice(timestamp.length -5, timestamp.length);
    this.randomID = randomID;
    const createTestDB = () => new Promise((resolve, reject) => {
      client.query(`CREATE DATABASE "pathfinder${randomID}"`, (err, res) => {
        console.log(err, res);
        client.end();
        if (err) return reject(err);
        else resolve(res);
      });
    });
    await createTestDB();
    const DB_CONNECTION_STRING = process.env.DB_CONNECTION_STRING_DEV + randomID;
    console.log('DB_CONNECTION_STRING', DB_CONNECTION_STRING);
    this.instance = new Sequelize(`${DB_CONNECTION_STRING}?sslmode=require`, {
      logging: false,
    });
    await this.migrateTables();
    return this.instance;
  }
  async createTestDBStandalone() {
    const client = new Client({
      user: process.env.POSTGRES_USER,
      password: process.env.POSTGRES_PASSWORD,
      host: process.env.POSTGRES_HOST,
      database: "postgres",
      port: process.env.POSTGRES_PORT,
    });
    await client.connect();
    const timestamp = String(new Date().getTime())
    const randomID = String(Math.random()).replace(".", '').slice(0, 5) + timestamp.slice(timestamp.length -5, timestamp.length);
    const createTestDB = () => new Promise((resolve, reject) => {
      client.query(`CREATE DATABASE "pathfinder${randomID}"`, (err, res) => {
        // console.log(err, res);
        client.end();
        if (err) return reject(err);
        else resolve(res);
      });
    });
    await createTestDB();
    const DB_CONNECTION_STRING = process.env.DB_CONNECTION_STRING_DEV + randomID;
    console.log('DB_CONNECTION_STRING', DB_CONNECTION_STRING);
    return {
      randomID,
      connectionString: DB_CONNECTION_STRING,
    };
  }
  async removeTestDBStandalone(randomID) {
    const client = new Client({
      user: process.env.POSTGRES_USER,
      password: process.env.POSTGRES_PASSWORD,
      host: process.env.POSTGRES_HOST,
      database: "postgres",
      port: process.env.POSTGRES_PORT,
    });
    await client.connect();
    const deleteTestDB = () => new Promise((resolve, reject) => {
      client.query(`DROP DATABASE "pathfinder${randomID}" WITH (force)`, (err, res) => {
        // console.log(err, res);
        client.end();
        if (err) return reject(err);
        else resolve(res);
      });
      // // revoke connect
      // client.query(`REVOKE CONNECT ON DATABASE "pathfinder${randomID}" FROM public`, (err, res) => {
      //   // console.log(err, res);
      //   if (err) return reject(err);
        
      // });
    });
    await deleteTestDB();
  }
  async removeTest() {
    if (!this.randomID) return;
    if (this.instance) await this.instance.close();
    const client = new Client({
      user: process.env.POSTGRES_USER,
      password: process.env.POSTGRES_PASSWORD,
      host: process.env.POSTGRES_HOST,
      database: "postgres",
      port: process.env.POSTGRES_PORT,
    });
    await client.connect();
    const deleteTestDB = () => new Promise((resolve, reject) => {
      client.query(`DROP DATABASE "pathfinder${this.randomID}"`, (err, res) => {
        // console.log(err, res);
        client.end();
        if (err) return reject(err);
        else resolve(undefined);
      });
    });
    await deleteTestDB();
  }

  User : UserStatic;
  GeneralData : GeneralDataStatic;
  Client : ClientStatic;
  Pathfinder: PathfinderStatic;
  Element: ElementStatic;
  Statement: StatementStatic;
  Learning: LearningStatic;
  Learner: LearnerStatic;
  LearnerLikerts: LearnerLikertsStatic;
  Image: ImageStatic;

  queryInterface: any;

  async migrateTables() {
    this.User = await createUser(this.instance);
    this.GeneralData = await createGeneralData(this.instance);
    this.Client = await createClient(this.instance);
    this.Pathfinder = await createPathfinder(this.instance);
    this.Element = await createElement(this.instance);
    this.Statement = await createStatement(this.instance);
    this.Learning = await createLearning(this.instance, this.Statement);
    this.Learner = await createLearner(this.instance);
    this.LearnerLikerts = await createLearnerLikerts(this.instance);
    this.Image = await createImage(this.instance);
    await migration(this);
    await seed(this);
    this.makeReady();
  }
}

export default new DB();
