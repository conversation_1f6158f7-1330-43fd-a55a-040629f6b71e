import { TLearning } from "./TypeLearning";

export type TStatement = {
  id: string;
  clientId: string;
  pathfinderId: string;
  orderIndex?: number;
  statement: string;
  rolloverLikert1?: string;
  rolloverLikert2?: string;
  rolloverLikert3?: string;
  elementIds?: string;

  learningsLikert1?: TLearning[];
  learningsLikert2?: TLearning[];
  learningsLikert3?: TLearning[];
  learningIdsLikert1?: string; // a,b,c
  learningIdsLikert2?: string;
  learningIdsLikert3?: string;

  data?: any;
  deletedAt?: string;

  // db fields
  createdAt?: string;
  updatedAt?: string;
}
