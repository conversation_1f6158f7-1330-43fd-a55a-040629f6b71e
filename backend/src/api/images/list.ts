import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { checkAuthenOptional, combineMiddlewares, validateClientId } from "api/api-middlewares";
import { DB } from "db";
import { Var<PERSON><PERSON><PERSON> } from "helpers";
import Joi = require("joi");
import { Op } from "sequelize";

class ImageList implements TypeAPIHandler {

  url = "/api/images";
  method = "GET";
  apiSchema = {
    query: Joi.object({
      clientId: Joi.string().allow(''),
      name: Joi.string().allow(''),
      type: Joi.string().allow(''),
    })
  }

  preHandler = combineMiddlewares([
    checkAuthenOptional,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { name, type, clientId: cId } = request.query;

    const clientId = await validateClientId(request, cId);

    let list = await DB.Image.findAll({
      where: VarHelper.removeUndefinedField({
        clientId,
        name: name ? {
          [Op.like]: `%${name}%`, 
        } : undefined,
        type: type ? {
          [Op.like]: `%${type}%`, 
        } : undefined,
      }),
      order: [
        ['createdAt', 'DESC'],
      ],
    });

    return {
      success: true,
      data: list,
    }
  };
}

export default new ImageList();
