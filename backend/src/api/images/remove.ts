import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { checkAuthen, combineMiddlewares, validateRequest, validateClientId } from "api/api-middlewares";
import { DB } from "db";
import { ERROR } from 'const';
import Joi = require("joi");

class RemoveImage implements TypeAPIHandler {
  url = "/api/images/remove";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      id: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { id } = request.body;

    const image = await DB.Image.findByPk(id);
    if (!image) throw new Error(ERROR.NOT_EXISTED);

    if (request.user.role !== 'admin') {
      await validateClientId(request, image.clientId);
    }

    await image.destroy();

    return {
      success: true,
    }
  };
}

export default new RemoveImage();
