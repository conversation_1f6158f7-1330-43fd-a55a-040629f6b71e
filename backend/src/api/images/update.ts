import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { checkA<PERSON>en, combineMiddlewares, validateClientId, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { ERROR } from 'const';
import Joi = require("joi");

class UpdateImage implements TypeAPIHandler {
  url = "/api/images/update";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      id: Joi.string().required(),
      name: Joi.string().allow(''),
      type: Joi.string().allow(''),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { id, ...restParams } = request.body;

    const image = await DB.Image.findByPk(id);
    if (!image) throw new Error(ERROR.NOT_EXISTED);
    await validateClientId(request, image.clientId);

    for (let key in restParams) {
      if (restParams[key] !== undefined) {
        image[key] = restParams[key];
      }
    }

    await image.save();

    return {
      success: true,
      data: image,
    }
  };
}

export default new UpdateImage();
