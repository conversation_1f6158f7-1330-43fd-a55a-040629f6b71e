import { <PERSON>AP<PERSON><PERSON><PERSON><PERSON> } from 'type';
import { validateRequest, combineMiddlewares, checkAuthen, validateClientId } from '../api-middlewares'
import { DB } from 'db';
import { <PERSON>ar<PERSON><PERSON><PERSON> } from 'helpers';
import Joi = require("joi");

class CreateImage implements TypeAPIHandler {
  url = '/api/images/create';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      id: Joi.string().allow(''),
      name: Joi.string().allow(''),
      clientId: Joi.string().allow(''),
      url: Joi.string().allow(''),
      type: Joi.string().allow(''),
      data: Joi.object(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request, reply) => {
    const { id, name, clientId: cId, url, type, data } = request.body;

    const clientId = await validateClientId(request, cId);

    const element = await DB.Image.create({
      id: id || VarHelper.genId(),
      clientId,
      name,
      url,
      type,
      data,
    });

    return {
      success: true,
      data: element,
    }
  }
}

export default new CreateImage();
