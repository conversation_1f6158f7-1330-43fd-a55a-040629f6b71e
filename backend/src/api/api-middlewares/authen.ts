import { InMemory as RedisCache } from 'db';
import { ERROR } from 'const';

export const checkAuthen = async function (request, reply, next) {
  var jwt = request.headers['Authorization'] || request.headers['authorization'];
  let serverSK = `secret_${global.mainRegionEndpoint}`;
  if (jwt === serverSK) {
    request.isServerSide = true;
    return next();
  }
  if (!jwt) {
    reply.send({ success: false, error: ERROR.NOT_AUTHEN });
    return reply;
  }
  var userInfo = await RedisCache.hgetAsync('JWT', jwt);
  if (userInfo) {
    userInfo = JSON.parse(userInfo);
    request.user = userInfo;
    return next();
  }

  reply.send({ success: false, error: ERROR.NOT_AUTHEN });
  return reply;
};

export const checkAuthenLearner = async function (request, reply, next) {
  var jwt = request.headers['Learner'] || request.headers['learner'];
  if (!jwt) {
    reply.send({ success: false, error: ERROR.NOT_AUTHEN });
    return reply;
  }
  var learnerInfo = await RedisCache.hgetAsync('JWT', jwt);
  if (learnerInfo) {
    learnerInfo = JSON.parse(learnerInfo);
    request.learner = learnerInfo;
    return next();
  }

  reply.send({ success: false, error: ERROR.NOT_AUTHEN });
  return reply;
};

export const checkAuthenOptional = async function (request, reply, next) {
  var jwt = request.headers['Authorization'] || request.headers['authorization'];
  if (!jwt) return next();
  var userInfo = await RedisCache.hgetAsync('JWT', jwt);
  if (userInfo) {
    userInfo = JSON.parse(userInfo);
    request.user = userInfo;
    return next();
  }
  return next();
};

export const checkAdmin = async function (request, reply, next) {
  if (request.isServerSide) return next();
  if (!request.user) {
    reply.send({ success: false, error: ERROR.NOT_AUTHEN });
    return reply;
  }
  if (request.user.role !== 'admin') {
    reply.send({ success: false, error: ERROR.PERMISSION_DENIED });
    return reply;
  }
  return next();
};

export const checkClientAdmin = async function (request, reply, next) {
  if (request.isServerSide) return next();
  if (!request.user) {
    reply.send({ success: false, error: ERROR.NOT_AUTHEN });
    return reply;
  }
  if (request.user.role !== 'client') {
    reply.send({ success: false, error: ERROR.PERMISSION_DENIED });
    return reply;
  }
  return next();
};

export const checkServerRequest = async function (request, reply, next) {
  var jwt = request.headers['Authorization'] || request.headers['authorization'];
  let serverSK = `secret_${global.mainRegionEndpoint}`;
  if (jwt === serverSK) {
    request.isServerSide = true;
    return next();
  }
  reply.send({ success: false, error: ERROR.NOT_AUTHEN });
  return reply;
};
