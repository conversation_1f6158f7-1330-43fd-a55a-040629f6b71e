const h = (host = "", path = "") => {
  let url = host + path;
  if (host.endsWith("/") && path.startsWith("/")) {
    url = host + path.slice(1);
  }
  return url;
};

const makeHeader = (
  method: "GET" | "POST" | "PUT" | "PATCH" | "DELETE",
) => {
  let token = `secret_${global.mainRegionEndpoint}`;
  let headers = new Headers({
    Accept: "application/json, text/plain, */*",
  });
  if (token) headers.set("Authorization", token);
  if (method === "POST" || method === "PUT")
    headers.set("Content-Type", "application/json");
  return headers;
};

export const sendToSubDB = async (url, data) => {
  if (!global.isMainServer || !global.subRegionEndpoint) return;
  fetch(h(global.subRegionEndpoint, url), {
    method: "POST",
    headers: makeHeader("POST"),
    body: JSON.stringify(data),
  }).catch((e) => { 
    console.log("sync_sub_server_err", url, data, e);
  })
}

export const syncWithSubDB = async (request, reply, next) => {
  if (!global.subRegionEndpoint) return next();
  if (request.method === "POST") {
    fetch(h(global.subRegionEndpoint, request.url), {
      method: "POST",
      headers: makeHeader("POST"),
      body: JSON.stringify(request.body || {}),
    }).catch((e) => { 
      console.log("sync_sub_server_err", request.url, e);
    })
  }
  if (request.method === "PUT") {
    fetch(h(global.subRegionEndpoint, request.url), {
      method: "PUT",
      headers: makeHeader("POST"),
      body: JSON.stringify(request.body || {}),
    }).catch((e) => { 
      console.log("sync_sub_server_err", request.url, e);
    })
  }
  return next();
}
