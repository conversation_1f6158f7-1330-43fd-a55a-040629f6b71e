const util = require("util");
const { pipeline } = require("stream");
const pump = util.promisify(pipeline);
const path = require("path");
const fs = require("fs");

type TRegularFile = any;
export type TFileField = {
  type: "file";
  fileName: string;
  mimeType: string;
  filePath: string;
};

export interface IModifiedBody {
  [filed: string]: TRegularFile | TFileField;
}

// this will alter request.body (multipart/form-data)
export const receiveFileAndFields = async function (request, reply, next) {
  try {
    const contentType =
      request.headers["content-type"] || request.headers["Content-Type"];
    if (
      contentType.includes("application/json") ||
      contentType.includes("application/x-www-form-urlencoded")
    ) {
      next();
      return;
    }
    const modyfiedBody: IModifiedBody = {};
    for (let key in request.body) {
      if (!request.body[key].toBuffer) {
        modyfiedBody[key] = request.body[key].value;
        continue;
      }
      const buffer = await request.body[key].toBuffer();
      const fileName = new Date().getTime() + request.body[key].filename;
      const filePath = path.join(
        __dirname,
        "../../uploads/" + fileName
      );
      fs.writeFileSync(filePath, buffer);
      modyfiedBody[key] = {
        type: "file",
        fileName,
        mimeType: request.body[key].mimetype,
        filePath,
      };
    }
    request.body = modyfiedBody;
    next();
  } catch (err) {
    console.log(err);
  }
};
