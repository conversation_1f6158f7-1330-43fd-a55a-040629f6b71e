import { ERROR } from "const"
import { TRequestUser } from "type"

export const validateClientId = async (request: TRequestUser, id?: string) => {
  let clientId;
  if (!request.user) return id;
  if (request.user.role === 'admin') {
    clientId = id;
  }
  if (request.user.role === 'client') {
    clientId = request.user.clientId
    if (id && clientId !== id) {
      throw new Error(ERROR.PERMISSION_DENIED);
    }
  }
  if (!clientId) throw new Error(ERROR.CLIENT_ID_REQUIRED);
  return clientId;
}
