import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { checkAuthenOptional, combineMiddlewares, validateClientId } from "api/api-middlewares";
import { DB } from "db";
import { VarHelper } from "helpers";
import Joi = require("joi");
import { ERROR } from "const";
import { Op } from "sequelize";

class StatementList implements TypeAPIHandler {

  url = "/api/statements";
  method = "GET";
  apiSchema = {
    query: Joi.object({
      pathfinderId: Joi.string(),
      elementId: Joi.string(),
      statement: Joi.string(),
      all: Joi.boolean(),
    })
  }

  preHandler = combineMiddlewares([
    checkAuthenOptional,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { pathfinderId, elementId, statement, all } = request.query;

    if (pathfinderId) {
      const pathfinder = await DB.Pathfinder.findByPk(pathfinderId);
      if (!pathfinder) throw new Error(ERROR.NOT_EXISTED);
      await validateClientId(request, pathfinder.clientId);
    }
    if (!pathfinderId && request.user.role !== 'admin') {
      throw new Error(ERROR.PERMISSION_DENIED);
    }
  
    const includeDeleted = all && ['client', 'admin'].includes(request.user?.role);

    const list = await DB.Statement.findAll({
      where: VarHelper.removeUndefinedField({
        pathfinderId,
        statement,
        elementIds: elementId ? {
          [Op.like]: `%${elementId}%`, 
        } : undefined,
      }),
      order: [
        ['orderIndex', 'ASC'],
        ['createdAt', 'DESC'],
      ],
      paranoid: includeDeleted ? false : true,
    });

    return {
      success: true,
      data: list,
    }
  };
}

export default new StatementList();
