import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { checkAuthen, combineMiddlewares, validateClientId, validateRequest } from "api/api-middlewares";
import { StatementImportItem } from "api/pathfinders/import";
import { DB } from "db";
import Joi = require("joi");
import { Var<PERSON>elper } from "helpers";
import { ERROR } from "const";

class ImportStatement implements TypeAPIHandler {
  url = "/api/statements/import";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      pathfinderId: Joi.string().required(),
      statements: Joi.array().items(
        Joi.object({
          statement: Joi.string().allow(''),
          rollover1: Joi.string().allow(''),
          rollover2: Joi.string().allow(''),
          rollover3: Joi.string().allow(''),
          element: Joi.string().allow(''),
        }).unknown(true)
      ).required(),
      updateExisting: Joi.boolean(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { statements, updateExisting, pathfinderId } = request.body;

    const pathfinder = await DB.Pathfinder.findByPk(pathfinderId);
    if (!pathfinder) throw new Error(ERROR.NOT_EXISTED);
    if (request.user.role !== 'admin') {
      await validateClientId(request, pathfinder.clientId);
    }

    if (statements?.length) {
      await Promise.all(statements.reverse().map((statement: StatementImportItem) =>
        new Promise(async (resolve) => {
          let existedStatement;
          let newStatement;
          if (updateExisting) {
            existedStatement = await DB.Statement.findOne({
              where: {
                statement: statement.statement,
                pathfinderId,
              }
            })
          }

          const elementNames = statement.element?.split(',')?.map(i => i?.trim()?.toLowerCase());
          const elements = await DB.Element.findAll({
            where: VarHelper.removeUndefinedField({
              pathfinderId: pathfinderId,
            })
          })
          const elementIds = statement.element ? elements.filter(el => elementNames.includes(el.name.toLowerCase())).map(el => el.id).join(',') : undefined;

          const statementData = {
            pathfinderId,
            statement: statement.statement,
            rolloverLikert1: statement.rollover1,
            rolloverLikert2: statement.rollover2,
            rolloverLikert3: statement.rollover3,
            elementIds,
          }
          if (existedStatement && updateExisting) {
            newStatement = await existedStatement.update(statementData);
          } else {
            newStatement = await DB.Statement.create({
              id: VarHelper.genId(),
              clientId: pathfinder.clientId,
              ...statementData
            });
          }
          resolve(newStatement);
        })
      ))
    }

    return {
      success: true,
    }
  };
}

export default new ImportStatement();
