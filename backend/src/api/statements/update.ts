import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { checkAuthen, combineMiddlewares, validateClientId, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { ERROR } from 'const';
import Joi = require("joi");

const parseIdsToArr = (str: string) => {
  if (!str) return [];
  return str.split(',').map(i => String(i).trim())
}

class UpdateStatement implements TypeAPIHandler {
  url = "/api/statements/update";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      id: Joi.string().required(),
      orderIndex: Joi.number(),
      statement: Joi.string().allow(''),
      rolloverLikert1: Joi.string().allow(''),
      rolloverLikert2: Joi.string().allow(''),
      rolloverLikert3: Joi.string().allow(''),
      learningIdsLikert1: Joi.string().allow(''),
      learningIdsLikert2: Joi.string().allow(''),
      learningIdsLikert3: Joi.string().allow(''),
      elementIds: Joi.string().allow(''),
      data: Joi.object(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const {
      id,
      learningIdsLikert1,
      learningIdsLikert2,
      learningIdsLikert3,
      ...restParams
    } = request.body;

    const statement = await DB.Statement.findByPk(id);
    if (!statement) throw new Error(ERROR.NOT_EXISTED);
    await validateClientId(request, statement.clientId);

    const learningIdsObj = { learningIdsLikert1, learningIdsLikert2, learningIdsLikert3 };

    await Promise.all(
      [1, 2, 3].map(async idx => {
        if (
          learningIdsObj[`learningIdsLikert${idx}`] !== undefined &&
          learningIdsObj[`learningIdsLikert${idx}`] !== statement[`learningIdsLikert${idx}`]
        ) {
          const learnings = await Promise.all(
            parseIdsToArr(learningIdsObj[`learningIdsLikert${idx}`]).map(id => {
              return DB.Learning.findByPk(id)
            })
          );
          if (learnings.some(i => !i)) {
            throw new Error(ERROR.INVALID_LEARNING_ID);
          }
          statement[`learningIdsLikert${idx}`] = learningIdsObj[`learningIdsLikert${idx}`];
          statement[`learningsLikert${idx}`] = learnings;
        }
      })
    )

    for (let key in restParams) {
      if (restParams[key] !== undefined) {
        statement[key] = restParams[key];
      }
    }
    await statement.save();

    return {
      success: true,
      data: statement,
    }
  };
}

export default new UpdateStatement();
