import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'type';
import { validateRequest, combineMiddlewares, checkAuthen, validateClientId } from '../api-middlewares'
import { DB } from 'db';
import { <PERSON>ar<PERSON><PERSON><PERSON> } from 'helpers';
import Joi = require("joi");
import { ERROR } from 'const';

const parseIdsToArr = (str: string) => {
  if (!str) return [];
  return str.split(',').map(i => String(i).trim())
}

class CreateStatement implements TypeAPIHandler {
  url = '/api/statements/create';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      id: Joi.string().allow(''),
      clientId: Joi.string().allow(''),
      pathfinderId: Joi.string().required(),
      orderIndex: Joi.number(),
      statement: Joi.string().required(),
      rolloverLikert1: Joi.string().allow(''),
      rolloverLikert2: Joi.string().allow(''),
      rolloverLikert3: Joi.string().allow(''),
      learningIdsLikert1: Joi.string().allow(''),
      learningIdsLikert2: Joi.string().allow(''),
      learningIdsLikert3: Joi.string().allow(''),
      elementIds: Joi.string().allow(''),
      data: Joi.object(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request, reply) => {
    const {
      id,
      clientId: cId,
      pathfinderId,
      orderIndex,
      statement,
      rolloverLikert1, rolloverLikert2, rolloverLikert3,
      elementIds,
      learningIdsLikert1, learningIdsLikert2, learningIdsLikert3,
      data,
    } = request.body;

    const clientId = await validateClientId(request, cId);
    const pathfinder = await DB.Pathfinder.findByPk(pathfinderId);
    if (!pathfinder) throw new Error(ERROR.NOT_EXISTED);
    if (pathfinder.clientId !== clientId) throw new Error(ERROR.PERMISSION_DENIED);

    const learningIdsObj = { learningIdsLikert1, learningIdsLikert2, learningIdsLikert3 };
    const learningsObj = {};

    await Promise.all(
      [1, 2, 3].map(async idx => {
        const learnings = await Promise.all(
          parseIdsToArr(learningIdsObj[`learningIdsLikert${idx}`]).map(id => {
            return DB.Learning.findByPk(id)
          })
        );
        if (learnings.some(i => !i)) {
          throw new Error(ERROR.INVALID_LEARNING_ID);
        }
        learningsObj[idx] = learnings;
      })
    );

    const newStatement = await DB.Statement.create({
      id: id || VarHelper.genId(),
      clientId,
      pathfinderId,
      orderIndex,
      statement,
      rolloverLikert1, rolloverLikert2, rolloverLikert3,
      elementIds,
      learningsLikert1: learningsObj[1],
      learningsLikert2: learningsObj[2],
      learningsLikert3: learningsObj[3],
      learningIdsLikert1, learningIdsLikert2, learningIdsLikert3,
      data,
    });

    return {
      success: true,
      data: newStatement,
    }
  }
}

export default new CreateStatement();
