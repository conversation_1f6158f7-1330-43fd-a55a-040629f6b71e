import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { checkAuthen, combineMiddlewares, validateRequest, validateClientId } from "api/api-middlewares";
import { DB } from "db";
import { ERROR } from 'const';
import Joi = require("joi");

class RemoveStatement implements TypeAPIHandler {
  url = "/api/statements/remove";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      id: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { id } = request.body;

    const statement = await DB.Statement.findByPk(id);
    if (!statement) throw new Error(ERROR.NOT_EXISTED);

    if (request.user.role !== 'admin') {
      await validateClientId(request, statement.clientId);
    }

    await statement.destroy();

    return {
      success: true,
    }
  };
}

export default new RemoveStatement();
