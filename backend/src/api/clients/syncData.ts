import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { combineMiddlewares } from "api/api-middlewares";
import { DB } from "db";
import Joi = require("joi");
import { checkServerRequest } from "api/api-middlewares/authen";

class SyncClient implements TypeAPIHandler {
  url = "/api/clients/sync";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      id: Joi.string().allow(''),
      name: Joi.string().allow(),
      logo: Joi.string().allow(''),
      location: Joi.string().allow(''),
      slug: Joi.string().allow(''),
      region: Joi.string().allow(''),
      data: Joi.object(),
    }),
  }

  preHandler = combineMiddlewares([
    checkServerRequest,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const params = request.body;

    const client = await DB.Client.findByPk(params.id);

    if (!client) {
      await DB.Client.create(params);
    } else {
      for (let key in params) {
        client[key] = params[key];
      }
      await client.save();
    }

    return {
      success: true,
    }
  };
}

export default new SyncClient();
