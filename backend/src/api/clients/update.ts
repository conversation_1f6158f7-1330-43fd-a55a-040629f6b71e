import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { check<PERSON><PERSON>en, combineMiddlewares, validateClientId, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { ERROR } from 'const';
import Joi = require("joi");

class UpdateClient implements TypeAPIHandler {
  url = "/api/clients/update";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      id: Joi.string().allow(''),
      name: Joi.string().allow(''),
      logo: Joi.string().allow(''),
      location: Joi.string().allow(''),
      data: Joi.object(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { id, ...restParams } = request.body;
    
    const clientId = await validateClientId(request, id);

    const client = await DB.Client.findByPk(clientId);
    if (!client) throw new Error(ERROR.NOT_EXISTED);

    for (let key in restParams) {
      if (restParams[key] !== undefined) {
        client[key] = restParams[key];
      }
    }
    await client.save();

    return {
      success: true,
      data: client,
    }
  };
}

export default new UpdateClient();
