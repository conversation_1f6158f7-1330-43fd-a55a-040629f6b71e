import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import Joi = require("joi");
import { ERROR } from "const";
import { <PERSON>ar<PERSON><PERSON><PERSON> } from "helpers";
import { Op } from "sequelize";

class SiteInfo implements TypeAPIHandler {

  url = "/api/site-info/:slug";
  method = "GET";
  apiSchema = {
    params: Joi.object({
      slug: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { slug } = request.params;
    const client = await DB.Client.findOne({
      where: {
        [Op.or]: [
          { slug },
          { id: slug },
        ]
      }
    });

    if (!client) throw new Error(ERROR.NOT_EXISTED);

    const url = VarHelper.replacePlaceholders(global.frontendPathfinderDomain, {
      slug: client.slug,
      region: client.region,
    })

    return {
      success: true,
      data: {
        ...client.toJSON(),
        apiUrl: VarHelper.getEndpointByRegion(client.region),
        url,
      },
    }
  };
}

export default new SiteInfo();
