import { TRe<PERSON>User, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'type';
import { validateRequest, combineMiddlewares, checkAuthen } from '../api-middlewares'
import { ERROR } from 'const';
import { DB } from 'db';
import { <PERSON>the<PERSON><PERSON><PERSON><PERSON>, Var<PERSON>elper } from 'helpers';
import Joi = require("joi");
import { checkClientAdmin } from 'api/api-middlewares/authen';

class CreateAccount implements TypeAPIHandler {
  url = '/api/clients/create-account';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      email: Joi.string().required(),
      password: Joi.string().required(),
      firstName: Joi.string().required(),
      lastName: Joi.string().allow(''),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
    checkClientAdmin,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { email, password, firstName, lastName } = request.body;

    if (!request.user.clientId) {
      throw new Error(ERROR.CLIENT_NOT_AVAILABLE);
    }

    const find = await DB.User.findOne({
      where: { email: email.toLowerCase() },
    });
    if (find) throw new Error(ERROR.ACCOUNT_EXISTED);

    const hashedPassword = await AuthenHelper.hashPassword(password);

    const user = await DB.User.create({
      id: VarHelper.genId(),
      email: email.toLowerCase(),
      password: hashedPassword as string,
      firstName, lastName,
      role: 'client',
      clientId: request.user.clientId,
    });

    const data = Object.assign(JSON.parse(JSON.stringify(user)), {
      password: undefined,
      resetPasswordCode: undefined,
    });

    return {
      success: true,
      data,
    }
  }
}

export default new CreateAccount();
