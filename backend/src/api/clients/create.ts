import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'type';
import { validateRequest, combineMiddlewares, checkAuthen, checkAdmin } from '../api-middlewares'
import { DB } from 'db';
import { Var<PERSON><PERSON>per } from 'helpers';
import Joi = require("joi");
import { ERROR } from 'const';

class CreateClient implements TypeAPIHandler {
  url = '/api/clients/create';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      name: Joi.string().required(),
      logo: Joi.string().allow(''),
      location: Joi.string().allow(''),
      slug: Joi.string().required(),
      region: Joi.string().required(),
      data: Joi.object(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
    checkAdmin,
  ]);

  handler = async (request, reply) => {
    const { name, logo, location, data, region, slug } = request.body;

    const existed = await DB.Client.findOne({
      where: {
        slug,
      }
    })
    if (existed) throw new Error(ERROR.CLIENT_SLUG_EXISTED);
  
    const client = await DB.Client.create({
      id: VarHelper.genId(),
      name,
      logo,
      location,
      data,
      region,
      slug,
    });

    return {
      success: true,
      data: client,
    }
  }
}

export default new CreateClient();
