import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { check<PERSON><PERSON><PERSON>, checkAdmin, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { ERROR } from 'const';
import * as moment from 'moment-timezone';
import Joi = require("joi");

class DeleteClient implements TypeAPIHandler {
  url = "/api/clients/remove";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      id: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
    checkAdmin,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { id } = request.body;

    const client = await DB.Client.findByPk(id);
    if (!client) throw new Error(ERROR.NOT_EXISTED);

    await client.destroy();

    await DB.User.destroy({
      where: {
        clientId: id
      }
    });

    return {
      success: true,
      data: client,
    }
  };
}

export default new DeleteClient();
