import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import Joi = require("joi");
import { ERROR } from "const";

class Detail implements TypeAPIHandler {

  url = "/api/clients/:id";
  method = "GET";
  apiSchema = {
    params: Joi.object({
      id: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { id } = request.params;
    const client = await DB.Client.findByPk(id);

    if (!client) throw new Error(ERROR.NOT_EXISTED);

    return {
      success: true,
      data: client,
    }
  };
}

export default new Detail();
