import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { checkAdmin, checkAuthen, checkAuthenOptional, combineMiddlewares } from "api/api-middlewares";
import { DB } from "db";
import { Var<PERSON><PERSON>per } from "helpers";
import Joi = require("joi");

class ClientList implements TypeAPIHandler {

  url = "/api/clients";
  method = "GET";
  apiSchema = {
    query: Joi.object({
      name: Joi.string(),
      location: Joi.string(),
      all: Joi.boolean(),
    })
  }

  preHandler = combineMiddlewares([
    checkAuthenOptional,
    checkAuthen,
    checkAdmin,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { name, location, all } = request.query;
    const list = await DB.Client.findAll({
      where: VarHelper.removeUndefinedField({
        name,
        location,
      }),
      order: [
        ['createdAt', 'DESC'],
      ],
      paranoid: all ? false : true,
    });

    return {
      success: true,
      data: list,
    }
  };
}

export default new ClientList();
