import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { checkAuthenOptional, combineMiddlewares } from "api/api-middlewares";
import { DB } from "db";
import { Var<PERSON><PERSON><PERSON> } from "helpers";
import Joi = require("joi");

class GeneralDataList implements TypeAPIHandler {

  url = "/api/general-data/:type";
  method = "GET";
  apiSchema = {
    query: Joi.object({
      userId: Joi.string(),
      name: Joi.string(),
      field1: Joi.string(),
      field2: Joi.string(),
    }),
    params: Joi.object({
      type: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    checkAuthenOptional,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { userId, name, field1, field2 } = request.query;
    const list = await DB.GeneralData.findAll({
      where: VarHelper.removeUndefinedField({
        userId,
        type: request.params['frontend-define-any'],
        name,
        field1,
        field2,
      }),
      order: [
        ['updatedAt', 'DESC'],
      ],
    });
    const filteredList = list.filter(val => {
      if (val.userId !== request.user?.id && !val.publicPermission.r) return false;
      return true;
    });

    return {
      success: true,
      data: filteredList,
    }
  };
}

export default new GeneralDataList();