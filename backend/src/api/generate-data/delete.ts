import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { checkA<PERSON>en, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { Var<PERSON><PERSON><PERSON> } from "helpers";
import { ERROR } from 'const';
import Joi = require("joi");

class GeneralDataList implements TypeAPIHandler {

  url = "/api/general-data/:type/:id";
  method = "DELETE";
  apiSchema = {
    params: Joi.object({
      id: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const find = await DB.GeneralData.findOne({
      where: {
        id: request.params.id,
        type: request.params.type,
      },
    });
    if (find.userId !== request.user?.id && !find.publicPermission.d) {
      throw new Error(ERROR.PERMISSION_DENIED);
    }
    await find.destroy();
    return {
      success: true,
    }
  };
}

export default new GeneralDataList();