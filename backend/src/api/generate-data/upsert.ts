import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { check<PERSON><PERSON><PERSON>, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { Var<PERSON><PERSON><PERSON> } from "helpers";
import { ERROR } from 'const';
import Joi = require("joi");

class GeneralDataCreate implements TypeAPIHandler {

  url = "/api/general-data/:type";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      id: Joi.string(),
      name: Joi.string(),
      field1: Joi.string(),
      field2: Joi.string(),
      publicPermission: Joi.string(),
      data: Joi.any(),
    }),
    params: Joi.object({
      type: Joi.string().required(),
    })
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    // UPDATE EXISTING
    if (request.body.id) {
      const find = await DB.GeneralData.findByPk(request.body.id);
      if (!!find) {
        if (find.userId !== request.user?.id && !find.publicPermission.u) {
          throw new Error(ERROR.PERMISSION_DENIED);
        }
        for (let key in request.body) {
          if (key === 'id' || key === 'userId') continue;
          find[key] = request.body[key];
        }
        await find.save();
        return { success: true, data: find };
      }
    }
    // CREATE NEW
    const data = await DB.GeneralData.create({
      id: VarHelper.genId(),
      ...request.body,
      type: request.params.type,
      userId: request.user?.id,
    });

    return {
      success: true,
      data,
    }
  };
}

export default new GeneralDataCreate();