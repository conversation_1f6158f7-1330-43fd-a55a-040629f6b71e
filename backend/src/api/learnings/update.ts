import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { check<PERSON><PERSON>en, combineMiddlewares, validateClientId, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { ERROR } from 'const';
import Joi = require("joi");

class UpdateLearning implements TypeAPIHandler {
  url = "/api/learnings/update";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      id: Joi.string().required(),
      name: Joi.string().allow(''),
      url: Joi.string().allow(''),
      clientId: Joi.string().allow(''),
      urlShortName: Joi.string().allow(''),
      icon: Joi.string().allow(''),
      type: Joi.string().allow(''),
      comment: Joi.string().allow(''),
      tags: Joi.string().allow(''),
      data: Joi.object(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { id, ...restParams } = request.body;

    const learning = await DB.Learning.findByPk(id);
    if (!learning) throw new Error(ERROR.NOT_EXISTED);

    await validateClientId(request, learning.clientId);

    for (let key in restParams) {
      if (restParams[key] !== undefined) {
        learning[key] = restParams[key];
      }
    }
    await learning.save();

    return {
      success: true,
      data: learning,
    }
  };
}

export default new UpdateLearning();
