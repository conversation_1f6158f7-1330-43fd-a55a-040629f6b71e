import { <PERSON>AP<PERSON>H<PERSON><PERSON> } from 'type';
import { validateRequest, combineMiddlewares, checkAuthen, validateClientId } from '../api-middlewares'
import { DB } from 'db';
import { Var<PERSON><PERSON><PERSON> } from 'helpers';
import Joi = require("joi");

class CreateLearning implements TypeAPIHandler {
  url = '/api/learnings/create';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      name: Joi.string().required(),
      urlShortName: Joi.string().allow(''),
      url: Joi.string().required(),
      clientId: Joi.string(),
      icon: Joi.string(),
      type: Joi.string(),
      comment: Joi.string(),
      tags: Joi.string(),
      data: Joi.object(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request, reply) => {
    const { name, url, clientId: cId, icon, type, comment, tags, data, urlShortName } = request.body;

    const clientId = await validateClientId(request, cId);

    const learning = await DB.Learning.create({
      id: VarHelper.genId(),
      clientId,
      name, url,
      icon, type, comment, tags,
      urlShortName,
      data,
    });

    return {
      success: true,
      data: learning,
    }
  }
}

export default new CreateLearning();
