import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { checkA<PERSON>en, combineMiddlewares, validateClientId } from "api/api-middlewares";
import { DB } from "db";
import { Var<PERSON><PERSON><PERSON> } from "helpers";
import Joi = require("joi");

const parseKeysToArr = (str: string) => {
  if (!str) return [];
  return str.split(',').map(i => String(i).trim().toLowerCase())
}

class LearningList implements TypeAPIHandler {

  url = "/api/learnings";
  method = "GET";
  apiSchema = {
    query: Joi.object({
      clientId: Joi.string(),
      name: Joi.string(),
      type: Joi.string(),
      tags: Joi.string(),
      all: Joi.boolean(),
      sortBy: Joi.string().valid('name', 'type', 'tags', 'name_desc', 'type_desc', 'tags_desc'),
    })
  }

  preHandler = combineMiddlewares([
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { clientId: cId, name, type, tags, all, sortBy } = request.query;

    const clientId = await validateClientId(request, cId);

    const includeDeleted = all && ['client', 'admin'].includes(request.user.role);

    let order = ['createdAt', 'DESC'];
    if (sortBy) {
      if (sortBy.endsWith('_desc')) {
        order = [sortBy.split('_')?.[0], 'DESC'];
      } else {
        order = [sortBy, 'ASC'];
      }
    }

    let list = await DB.Learning.findAll({
      where: VarHelper.removeUndefinedField({
        clientId,
        name,
        type,
      }),
      paranoid: includeDeleted ? false : true,
      order: [
        // @ts-ignore
        order
      ],
    });

    if (tags) {
      list = list.filter(i =>
        i.tags && !parseKeysToArr(tags).some((key) =>
          !parseKeysToArr(i.tags).includes(key)
        )
      )
    }

    return {
      success: true,
      data: list,
    }
  };
}

export default new LearningList();
