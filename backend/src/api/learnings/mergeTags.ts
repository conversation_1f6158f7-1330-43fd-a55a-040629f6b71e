import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { check<PERSON><PERSON>en, combineMiddlewares, validateClientId, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { ERROR } from 'const';
import Joi = require("joi");
import { Op } from "sequelize";
import { VarHelper } from "helpers";

class MergeTags implements TypeAPIHandler {
  url = "/api/learnings/merge-tags";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      clientId: Joi.string(),
      tags: Joi.string().required(),
      newTag: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { tags, newTag, clientId: cId, ...restParams } = request.body;

    const clientId = await validateClientId(request, cId);

    const learnings = await DB.Learning.findAll({
      where: {
        clientId,
        tags: {
          [Op.not]: null
        }
      }
    })
    const mergeTags = tags.split(',').map(tag => tag.trim());
    if (learnings) {
      await Promise.all(learnings?.map((learning) =>
        new Promise(async (resolve) => {
          const _oldTags = learning.tags;
          if (mergeTags.some(tag => _oldTags.includes(tag))) {
            learning.tags = VarHelper.regexReplaceArray(_oldTags, mergeTags, newTag);
          }
          await learning.save();
          resolve(true);
        })
      ))
    }

    return {
      success: true,
    }
  };
}

export default new MergeTags();
