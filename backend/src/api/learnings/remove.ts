import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { checkAuthen, combineMiddlewares, validateRequest, validateClientId } from "api/api-middlewares";
import { DB } from "db";
import { ERROR } from 'const';
import Joi = require("joi");

class RemoveLearning implements TypeAPIHandler {
  url = "/api/learnings/remove";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      id: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { id } = request.body;

    const learning = await DB.Learning.findByPk(id);
    if (!learning) throw new Error(ERROR.NOT_EXISTED);

    await validateClientId(request, learning.clientId);

    await learning.destroy();

    return {
      success: true,
    }
  };
}

export default new RemoveLearning();
