import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { check<PERSON><PERSON>en, combineMiddlewares, validateClientId, validateRequest } from "api/api-middlewares";
import { LearningImportItem } from "api/pathfinders/import";
import { DB } from "db";
import Joi = require("joi");
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "helpers";

class ImportPathfinder implements TypeAPIHandler {
  url = "/api/learnings/import";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      learnings: Joi.array().items(
        Joi.object({
          name: Joi.string().allow(''),
          url: Joi.string().allow(''),
          urlShortName: Joi.string().allow(''),
          icon: Joi.string().allow(''),
          type: Joi.string().allow(''),
          comment: Joi.string().allow(''),
          tags: Joi.string().allow(''),
        }).unknown(true)
      ).required(),
      updateExisting: Joi.boolean(),
      clientId: Joi.string(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { learnings, updateExisting, clientId: cId } = request.body;
    const clientId = await validateClientId(request, cId);

    if (learnings?.length) {
      await Promise.all(learnings?.map((learning: LearningImportItem) =>
        new Promise(async (resolve) => {
          let existedLearning;
          let newLearning;
          if (updateExisting) {
            existedLearning = await DB.Learning.findOne({
              where: {
                clientId,
                name: learning.name
              }
            })
          }
          let iconUrl = learning.icon;
          if (iconUrl && !iconUrl.startsWith(AWSHelper.uploadedDomain)) {
            iconUrl = await AWSHelper.uploadFileFromURL({
              url: iconUrl,
              key: VarHelper.genId(),
            })
          }
          let learningData = {
            name: learning.name,
            url: learning.url,
            icon: iconUrl,
            type: learning.type,
            comment: learning.comment,
            tags: learning.tags,
            urlShortName: learning.urlShortName,
          };
          if (existedLearning && updateExisting) {
            newLearning = await existedLearning.update(learningData);
          } else {
            newLearning = await DB.Learning.create({
              id: VarHelper.genId(),
              clientId,
              ...learningData
            })
          }
          resolve(newLearning);
        })
      ))
    }

    return {
      success: true,
    }
  };
}

export default new ImportPathfinder();
