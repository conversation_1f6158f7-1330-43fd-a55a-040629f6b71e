import { <PERSON><PERSON><PERSON><PERSON>, TR<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>AP<PERSON><PERSON><PERSON><PERSON> } from 'type';
import { validateRequest, combineMiddlewares } from '../api-middlewares'
import { ERROR } from 'const';
import { DB, InMemory as RedisCache } from 'db';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ar<PERSON><PERSON>per } from 'helpers';
import Joi = require("joi");
import { checkAuthenLearner } from 'api/api-middlewares/authen';
import { Op } from 'sequelize';
import { MailHelper } from 'helpers';

class SubmitLikert implements TypeAPIHandler {
  url = '/api/learners/submit';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      pathfinderId: Joi.string().required(),
      elementId: Joi.string().required(),
      likerts: Joi.object().required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthenLearner,
  ]);

  handler = async (request: TRequestLearner, reply) => {
    const { pathfinderId, elementId, likerts } = request.body;
    const learner = request.learner;

    const pathfinder = await DB.Pathfinder.findByPk(pathfinderId);
    if (!pathfinder) throw new Error(ERROR.NOT_EXISTED)

    const element = await DB.Element.findByPk(elementId);
    if (!element) throw new Error(ERROR.NOT_EXISTED)

    const statements = await DB.Statement.findAll({
      where: {
        pathfinderId,
        elementIds: {
          [Op.like]: `%${element.id}%`,
        },
      },
    })
    const likertsObj = {};
    let recommendLearnings = [];
    Object.keys(likerts).forEach(key => {
      const _statement = statements?.find(i => i.id === key);
      recommendLearnings = recommendLearnings.concat(_statement?.[`learningsLikert${likerts[key] + 1}`] || []);

      likertsObj[key] = {
        statement: _statement?.statement,
        likertIdx: likerts[key],
        likert: pathfinder?.[`likertScaleTitle${likerts[key] + 1}`],
        learnings: _statement?.[`learningsLikert${likerts[key] + 1}`]?.map((learning: TLearning) => ({
          name: learning.name,
          url: learning.url,
          urlShortName: learning.urlShortName,
          comment: learning.comment,
        }))
      }
    })

    const likert = await DB.LearnerLikerts.create({
      id: VarHelper.genId(),
      clientId: pathfinder?.clientId,
      learnerId: learner?.id,
      pathfinderId: pathfinder?.id,
      pathfinderName: pathfinder?.name,
      likerts: likertsObj,
      elementId,
    })

    if (pathfinder.sendEmail && learner.email) {
      const replaceData = {
        FirstName: learner.firstName,
        PathfinderName: pathfinder.name,
      }

      const emailContent = `
        ${VarHelper.replacePlaceholders(pathfinder.emailContent || '', replaceData)}
        ${MailHelper.genLearningsTableHtml(recommendLearnings)}
        ${VarHelper.replacePlaceholders(pathfinder.emailFooter || '', replaceData)}
      `;

      const _client = await DB.Client.findOne({
        where: {
          id: pathfinder.clientId
        }
      });

      await MailHelper.sendSMTPEmail({
        sender: _client?.name,
        clientSlug: _client?.slug,
        to: learner.email,
        cc: pathfinder.additionalRecipients ? pathfinder.additionalRecipients.split(',') : [],
        subject: pathfinder.emailSubject ? VarHelper.replacePlaceholders(pathfinder.emailSubject || '', replaceData) : "Learnings list",
        html: emailContent,
      })
    }

    return {
      success: true,
      data: likert,
    }
  }
}

export default new SubmitLikert();
