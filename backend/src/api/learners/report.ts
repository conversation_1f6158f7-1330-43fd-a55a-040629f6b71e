import { TRequestUser, <PERSON>AP<PERSON><PERSON><PERSON><PERSON> } from 'type';
import { validateRequest, combineMiddlewares, checkAuthen, validateClientId } from '../api-middlewares'
import { DB } from 'db';
import { VarHelper } from 'helpers';
import Joi = require("joi");
import { Op } from 'sequelize';

class LearnerReport implements TypeAPIHandler {
  url = '/api/learners/report';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      learnerId: Joi.string().allow(''),
      pathfinderId: Joi.string().allow(''),
      clientId: Joi.string().allow(''),
      fromDate: Joi.date().allow(''),
      toDate: Joi.date().allow(''),
      statementId: Joi.string().allow(''),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { pathfinderId, learnerId, clientId: cId, fromDate, toDate, statementId } = request.body;

    let clientId
    if (request.user.role !== 'admin') {
      clientId = await validateClientId(request, cId);
    }

    const data = await DB.LearnerLikerts.findAll({
      where: VarHelper.removeUndefinedField({
        learnerId,
        pathfinderId,
        clientId,
        createdAt: fromDate && toDate ? {
          [Op.between]: [fromDate, toDate],
        } : undefined,
        likerts: statementId ? {
          [statementId]: {
            [Op.not]: null
          }
        } : undefined,
      }),
    });

    return {
      success: true,
      data,
    }
  }
}

export default new LearnerReport();
