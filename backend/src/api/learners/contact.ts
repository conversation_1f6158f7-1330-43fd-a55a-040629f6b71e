import { <PERSON>AP<PERSON><PERSON><PERSON><PERSON> } from 'type';
import { validateRequest, combineMiddlewares } from '../api-middlewares'
import { ERROR } from 'const';
import { DB, InMemory as RedisCache } from 'db';
import { Authen<PERSON><PERSON>per, VarHelper } from 'helpers';
import Joi = require("joi");

class LearnerContact implements TypeAPIHandler {
  url = '/api/learners/contact';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      pathfinderId: Joi.string(),
      email: Joi.string().required(),
      firstName: Joi.string(),
      lastName: Joi.string(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
  ]);

  handler = async (request, reply) => {
    const { pathfinderId, email, firstName, lastName } = request.body;

    if (pathfinderId) {
      const pathfinder = await DB.Pathfinder.findByPk(pathfinderId);
      if (!pathfinder) throw new Error(ERROR.NOT_EXISTED)
    }

    let learner = await DB.Learner.findOne({
      where: { email: email.toLowerCase() },
    });
    if (learner) {
      learner.firstName = firstName;
      learner.lastName = lastName;
      await learner.save();
    } else {
      learner = await DB.Learner.create({
        id: VarHelper.genId(),
        email: email.toLowerCase(),
        firstName,
        lastName,
      })
    }

    const token = AuthenHelper.genJWTToken({ id: learner.id });
    const publicInfo = learner.toJSON();
    _afterLogin(publicInfo, token);

    let likerts;
    try {
      likerts = await DB.LearnerLikerts.findOne({
        where: VarHelper.removeUndefinedField({
          learnerId: learner.getDataValue("id"),
          pathfinderId,
        }),
        order: [
          ['createdAt', 'DESC'],
        ],
      });
    } catch (error) {
    }

    return {
      success: true,
      data: {
        token,
        publicInfo,
        likerts,
      }
    }
  }
}

async function _afterLogin(learner, token) {
  const listToken = await RedisCache.hgetAsync('JWTs', learner.id);
  if (listToken) {
    const parsedList = JSON.parse(listToken);
    if (!parsedList.includes(token)) {
      parsedList.push(token);
      await RedisCache.hsetAsync('JWTs', learner.id, JSON.stringify(parsedList));
    }
  } else {
    await RedisCache.hsetAsync('JWTs', learner.id, JSON.stringify([token]));
  }
  await RedisCache.hsetAsync('JWT', token, JSON.stringify(learner));
}

export default new LearnerContact();
