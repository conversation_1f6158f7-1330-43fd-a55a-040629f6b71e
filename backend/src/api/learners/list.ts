import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { checkAuthen, combineMiddlewares, validateClientId } from "api/api-middlewares";
import { DB } from "db";
import { Var<PERSON>elper } from "helpers";
import Joi = require("joi");

class LearnerList implements TypeAPIHandler {
  url = "/api/learners";
  method = "GET";
  apiSchema = {
    query: Joi.object({
      all: Joi.boolean(),
    })
  }

  preHandler = combineMiddlewares([
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { all } = request.query;

    const includeDeleted = all && ['client', 'admin'].includes(request.user.role);

    const data = await DB.Learner.findAll({
      where: {},
      paranoid: includeDeleted ? false : true,
      order: [
        ['createdAt', 'DESC'],
      ],
    });

    return {
      success: true,
      data,
    }
  };
}

export default new LearnerList();
