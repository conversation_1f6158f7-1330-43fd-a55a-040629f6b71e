import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { checkA<PERSON>en, combineMiddlewares, validateClientId, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import Joi = require("joi");
import { Op } from "sequelize";

class ExportPathfinder implements TypeAPIHandler {
  url = "/api/pathfinders/export";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      pathfinderIds: Joi.array().items(
        Joi.string()
      ),
      clientId: Joi.string(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { pathfinderIds, clientId: cId } = request.body;
    const clientId = await validateClientId(request, cId);

    let where: any = {}
    if (pathfinderIds?.length) {
      where.id = {
        [Op.in]: pathfinderIds,
      }
    } else if (clientId) {
      where.clientId = clientId;
    }

    const data = await DB.Pathfinder.findAll({
      where,
      order: [
        ['createdAt', 'DESC'],
      ],
    })

    const formatedData = await Promise.all(
      data.map(pathfinder => new Promise(async (resolve) => {
        let learningsObj = {}
        const elements = await DB.Element.findAll({
          where: {
            pathfinderId: pathfinder.id,
          },
          order: [
            ['orderIndex', 'ASC'],
            ['createdAt', 'DESC'],
          ],
        })
        const statements = await DB.Statement.findAll({
          where: {
            pathfinderId: pathfinder.id,
          },
          order: [
            ['orderIndex', 'ASC'],
            ['createdAt', 'DESC'],
          ],
        })

        for (const st of statements) {
          for (let learning of (st.learningsLikert1 || []).concat(st.learningsLikert2 || []).concat(st.learningsLikert3 || [])) {
            if (!learningsObj[learning.id]) {
              learningsObj[learning.id] = learning
            }
          }
        }
        resolve({
          id: pathfinder.id,
          additionalInstruction: pathfinder.additionalInstruction,
          completionInstruction: pathfinder.completionInstruction,
          welcomeInstruction: pathfinder.welcomeInstruction,
          elements: elements.map(el => ({
            filters: el.filterKeys,
            image: el.image,
            name: el.name,
            rolloverText: el.rolloverText,
          })),
          elementsTitle: pathfinder.elementsTitle,
          likertScale: `${pathfinder.likertScaleTitle1 || ''},${pathfinder.likertScaleTitle2 || ''},${pathfinder.likertScaleTitle3 || ''}`,
          logo: pathfinder.logo,
          name: pathfinder.name,
          learnings: Object.values(learningsObj),
          statements: statements.map(st => ({
            element: st.elementIds ?
              elements.filter(el =>
                st.elementIds.split(',').map(i => i?.trim()).includes(el.id)
              ).map(el => el.name).join(',')
              : '',
            learning1: st.learningsLikert1?.map(l => l.name).join('; '),
            learning2: st.learningsLikert2?.map(l => l.name).join('; '),
            learning3: st.learningsLikert3?.map(l => l.name).join('; '),
            learning1Ids: st.learningsLikert1?.map(l => l.id),
            learning2Ids: st.learningsLikert2?.map(l => l.id),
            learning3Ids: st.learningsLikert3?.map(l => l.id),
            rollover1: st.rolloverLikert1,
            rollover2: st.rolloverLikert2,
            rollover3: st.rolloverLikert3,
            statement: st.statement,
          }))
        });
      }))
    )

    return {
      success: true,
      data: formatedData,
    }
  };
}

export default new ExportPathfinder();
