import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { checkAuthen, combineMiddlewares, validateClientId } from "api/api-middlewares";
import { DB } from "db";
import { Var<PERSON><PERSON><PERSON> } from "helpers";
import Joi = require("joi");

class PathfinderList implements TypeAPIHandler {

  url = "/api/pathfinders";
  method = "GET";
  apiSchema = {
    query: Joi.object({
      clientId: Joi.string(),
      name: Joi.string(),
      all: Joi.boolean(),
    })
  }

  preHandler = combineMiddlewares([
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { clientId: cId, name, all } = request.query;

    let clientId;
    if (request.user.role !== 'admin') {
      clientId = await validateClientId(request, cId);
    }

    const includeDeleted = all && ['client', 'admin'].includes(request.user.role);

    let list = await DB.Pathfinder.findAll({
      where: VarH<PERSON>per.removeUndefinedField({
        clientId,
        name,
      }),
      order: [
        ['orderIndex', 'ASC'],
        ['createdAt', 'DESC'],
      ],
      paranoid: includeDeleted ? false : true,
    });

    return {
      success: true,
      data: list,
    }
  };
}

export default new PathfinderList();
