import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { checkAuthen, combineMiddlewares, validateRequest, validateClientId } from "api/api-middlewares";
import { DB } from "db";
import { ERROR } from 'const';
import Joi = require("joi");

class RemovePathfinder implements TypeAPIHandler {
  url = "/api/pathfinders/remove";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      id: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { id } = request.body;

    const pathfinder = await DB.Pathfinder.findByPk(id);
    if (!pathfinder) throw new Error(ERROR.NOT_EXISTED);

    await validateClientId(request, pathfinder.clientId);

    await pathfinder.destroy();

    return {
      success: true,
    }
  };
}

export default new RemovePathfinder();
