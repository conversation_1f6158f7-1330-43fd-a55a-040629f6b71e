import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { check<PERSON><PERSON>en, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { ERROR } from 'const';
import Joi = require("joi");

class UpdatePathfinder implements TypeAPIHandler {
  url = "/api/pathfinders/update";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      id: Joi.string().required(),
      name: Joi.string().allow(''),
      logo: Joi.string().allow(''),
      data: Joi.object(),
      orderIndex: Joi.number(),
      welcomeInstruction: Joi.string().allow(''),
      additionalInstruction: Joi.string().allow(''),
      completionInstruction: Joi.string().allow(''),
      listStatementTitle: Joi.string().allow(''),
      listStatementSubtitle: Joi.string().allow(''),
      listLearningTitle: Joi.string().allow(''),
      likertScaleTitle1: Joi.string().allow(''),
      likertScaleTitle2: Joi.string().allow(''),
      likertScaleTitle3: Joi.string().allow(''),
      elementsTitle: Joi.string().allow(''),
      disableElementsFilter: Joi.boolean(),
      sendEmail: Joi.boolean(),
      additionalRecipients: Joi.string().allow(''),
      emailSubject: Joi.string().allow(''),
      emailContent: Joi.string().allow(''),
      emailFooter: Joi.string().allow(''),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { id, ...restParams } = request.body;

    const pathfinder = await DB.Pathfinder.findByPk(id);
    if (!pathfinder) throw new Error(ERROR.NOT_EXISTED);

    for (let key in restParams) {
      if (restParams[key] !== undefined) {
        pathfinder[key] = restParams[key];
      }
    }
    await pathfinder.save();

    return {
      success: true,
      data: pathfinder,
    }
  };
}

export default new UpdatePathfinder();
