import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import Joi = require("joi");
import { ERROR } from "const";

class PathfinderDetail implements TypeAPIHandler {

  url = "/api/pathfinders/:id";
  method = "GET";
  apiSchema = {
    params: Joi.object({
      id: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
  ]);

  handler = async (request: TRequestUser) => {
    const { id } = request.params;
    const pathfinder = await DB.Pathfinder.findByPk(id);
    if (!pathfinder) throw new Error(ERROR.NOT_EXISTED);

    return {
      success: true,
      data: pathfinder,
    }
  };
}

export default new PathfinderDetail();
