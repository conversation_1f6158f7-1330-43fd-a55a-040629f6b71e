import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { checkA<PERSON>en, combineMiddlewares, validateClientId, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { ERROR } from 'const';
import Joi = require("joi");
import { VarHelper } from "helpers";
import _ = require("lodash");

class DuplicatePathfinder implements TypeAPIHandler {
  url = "/api/pathfinders/duplicate";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      pathfinderId: Joi.string().required(),
      clientId: Joi.string(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { pathfinderId, clientId: cId } = request.body;

    const pathfinder = await DB.Pathfinder.findByPk(pathfinderId);
    if (!pathfinder) throw new Error(ERROR.NOT_EXISTED);

    const clientId = await validateClientId(request, cId);

    const newPathfinder = await DB.Pathfinder.create({
      id: VarHelper.genId(),
      clientId,
      name: pathfinder.name,
      logo: pathfinder.logo,
      welcomeInstruction: pathfinder.welcomeInstruction,
      additionalInstruction: pathfinder.additionalInstruction,
      completionInstruction: pathfinder.completionInstruction,
      listStatementTitle: pathfinder.listStatementTitle,
      listStatementSubtitle: pathfinder.listStatementSubtitle,
      listLearningTitle: pathfinder.listLearningTitle,
      likertScaleTitle1: pathfinder.likertScaleTitle1,
      likertScaleTitle2: pathfinder.likertScaleTitle2,
      likertScaleTitle3: pathfinder.likertScaleTitle3,
      elementsTitle: pathfinder.elementsTitle,
      disableElementsFilter: pathfinder.disableElementsFilter,
      sendEmail: pathfinder.sendEmail,
      additionalRecipients: pathfinder.additionalRecipients,
      emailSubject: pathfinder.emailSubject,
      emailContent: pathfinder.emailContent,
      emailFooter: pathfinder.emailFooter,
      data: pathfinder.data,
    });

    const elements = await DB.Element.findAll({
      where: {
        pathfinderId: pathfinder.id,
      }
    });
    const elementsMapping = {};
    await Promise.all(elements?.map(element =>
      new Promise(async (resolve) => {
        const newElement = await DB.Element.create({
          id: VarHelper.genId(),
          clientId,
          name: element.name,
          pathfinderId: newPathfinder.id,
          rolloverText: element.rolloverText,
          image: element.image,
          filterKeys: element.filterKeys,
          orderIndex: element.orderIndex,
          data: element.data,
        })
        elementsMapping[element.id] = newElement.id;
        resolve(newElement);
      })
    ))

    const statements = await DB.Statement.findAll({
      where: {
        pathfinderId: pathfinder.id,
      }
    });
    const learningsMapping = {};

    await Promise.all(statements?.map(async (stm) => {
      const allLearnings = _.concat([], stm.learningsLikert1, stm.learningsLikert2, stm.learningsLikert3);
      for (let learning of allLearnings) {
        if (!learning) continue;
        if (!!learningsMapping[learning.id]) continue;
        if (pathfinder.clientId !== clientId) {
          const newLearning = await DB.Learning.create({
            id: VarHelper.genId(),
            clientId,
            name: learning.name,
            url: learning.url,
            icon: learning.icon,
            type: learning.type,
            comment: learning.comment,
            tags: learning.tags,
            data: learning.data,
            urlShortName: learning.urlShortName,
          })
          learningsMapping[learning.id] = newLearning;
        } else {
          learningsMapping[learning.id] = learning;
        }
      }
    }));

    await Promise.all(statements?.map(stm =>
      DB.Statement.create({
        id: VarHelper.genId(),
        clientId,
        pathfinderId: newPathfinder.id,
        orderIndex: stm.orderIndex,
        statement: stm.statement,
        rolloverLikert1: stm.rolloverLikert1,
        rolloverLikert2: stm.rolloverLikert2,
        rolloverLikert3: stm.rolloverLikert3,
        elementIds: stm.elementIds?.split(',').map(i => elementsMapping[String(i).trim()]).join(','),
        learningsLikert1: _.map(stm.learningsLikert1, i => learningsMapping[i.id]),
        learningsLikert2: _.map(stm.learningsLikert2, i => learningsMapping[i.id]),
        learningsLikert3: _.map(stm.learningsLikert3, i => learningsMapping[i.id]),
        learningIdsLikert1: stm.learningIdsLikert1?.split(',').map(i => learningsMapping?.[String(i).trim()]?.id).join(','),
        learningIdsLikert2: stm.learningIdsLikert2?.split(',').map(i => learningsMapping?.[String(i).trim()]?.id).join(','),
        learningIdsLikert3: stm.learningIdsLikert3?.split(',').map(i => learningsMapping?.[String(i).trim()]?.id).join(','),
        data: stm.data,
      })
    ))

    return {
      success: true,
      data: newPathfinder,
    }
  };
}

export default new DuplicatePathfinder();
