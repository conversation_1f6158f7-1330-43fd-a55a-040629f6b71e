import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { check<PERSON><PERSON>en, combineMiddlewares, validateClientId, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { ERROR } from 'const';
import Joi = require("joi");
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "helpers";

export interface LearningImportItem {
  name?: string
  url?: string
  icon?: string
  type?: string
  comment?: string
  tags?: string
  urlShortName?: string
}

export interface ElementImportItem {
  filters?: string
  image?: string
  name?: string
  rolloverText?: string
}

export interface StatementImportItem {
  element?: string
  learning1?: string
  learning2?: string
  learning3?: string
  rollover1?: string
  rollover2?: string
  rollover3?: string
  statement?: string
}

export interface PathfinderImportItem {
  additionalInstruction?: string
  completionInstruction?: string
  welcomeInstruction?: string
  elements?: ElementImportItem[]
  elementsTitle?: string
  likertScale?: string // "Always,Sometimes,Never"
  logo?: string
  name?: string
  statements?: StatementImportItem[]
}

class ImportPathfinder implements TypeAPIHandler {
  url = "/api/pathfinders/import";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      pathfinders: Joi.array().items(
        Joi.object({
          additionalInstruction: Joi.string().allow(''),
          completionInstruction: Joi.string().allow(''),
          welcomeInstruction: Joi.string().allow(''),
          elementsTitle: Joi.string().allow(''),
          likertScale: Joi.string().allow(''),
          logo: Joi.string().allow(''),
          name: Joi.string().allow(''),
          statements: Joi.array().items(
            Joi.object({
              element: Joi.string().allow(''),
              learning1: Joi.string().allow(''),
              learning2: Joi.string().allow(''),
              learning3: Joi.string().allow(''),
              rollover1: Joi.string().allow(''),
              rollover2: Joi.string().allow(''),
              rollover3: Joi.string().allow(''),
              statement: Joi.string().allow(''),
            })
          ),
          elements: Joi.array().items(
            Joi.object({
              filters: Joi.string().allow(''),
              image: Joi.string().allow(''),
              name: Joi.string().allow(''),
              rolloverText: Joi.string().allow(''),
            })
          )
        }).unknown(true)
      ).required(),
      learnings: Joi.array().items(
        Joi.object({
          name: Joi.string().allow(''),
          url: Joi.string().allow(''),
          urlShortName: Joi.string().allow(''),
          icon: Joi.string().allow(''),
          type: Joi.string().allow(''),
          comment: Joi.string().allow(''),
          tags: Joi.string().allow(''),
        }).unknown(true)
      ),
      updateExisting: Joi.boolean(),
      clientId: Joi.string(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { learnings, updateExisting, clientId: cId } = request.body;
    const clientId = await validateClientId(request, cId);
    const pathfinders = [...request.body.pathfinders] as PathfinderImportItem[];

    if (learnings?.length) {
      await Promise.all(learnings?.map((learning: LearningImportItem) =>
        new Promise(async (resolve) => {
          let existedLearning;
          let newLearning;
          if (updateExisting) {
            existedLearning = await DB.Learning.findOne({
              where: {
                clientId,
                name: learning.name
              }
            })
          }
          let iconUrl = learning.icon;
          if (iconUrl && !iconUrl.startsWith(AWSHelper.uploadedDomain)) {
            iconUrl = await AWSHelper.uploadFileFromURL({
              url: iconUrl,
              key: VarHelper.genId(),
            })
          }
          let learningData = {
            name: learning.name,
            url: learning.url,
            icon: iconUrl,
            type: learning.type,
            comment: learning.comment,
            tags: learning.tags,
            urlShortName: learning.urlShortName,
          };
          if (existedLearning && updateExisting) {
            newLearning = await existedLearning.update(learningData);
          } else {
            newLearning = await DB.Learning.create({
              id: VarHelper.genId(),
              clientId,
              ...learningData
            })
          }
          resolve(newLearning);
        })
      ))
    }

    for (let idx in pathfinders) {
      const pathfinder = pathfinders[idx];
      let existed;
      let newPathfinder;
      if (updateExisting) {
        existed = await DB.Pathfinder.findOne({
          where: {
            name: pathfinder.name
          }
        })
      }
      let logoUrl = pathfinder.logo;
      if (logoUrl && !logoUrl.startsWith(AWSHelper.uploadedDomain)) {
        logoUrl = await AWSHelper.uploadFileFromURL({
          url: logoUrl,
          key: VarHelper.genId(),
        })
      }

      const likertScales = pathfinder.likertScale?.split(',');
      const pathfinderData = {
        name: pathfinder.name,
        logo: logoUrl,
        welcomeInstruction: pathfinder.welcomeInstruction,
        additionalInstruction: pathfinder.additionalInstruction,
        completionInstruction: pathfinder.completionInstruction,
        likertScaleTitle1: likertScales?.[0]?.trim(),
        likertScaleTitle2: likertScales?.[1]?.trim(),
        likertScaleTitle3: likertScales?.[2]?.trim(),
        elementsTitle: pathfinder.elementsTitle,
      };
      if (existed && updateExisting) {
        newPathfinder = await existed.update(pathfinderData);
      } else {
        newPathfinder = await DB.Pathfinder.create({
          id: VarHelper.genId(),
          clientId,
          ...pathfinderData
        });
      }
      // handle elements
      if (pathfinder.elements?.length) {
        await Promise.all(pathfinder.elements?.map(element =>
          new Promise(async (resolve) => {
            let existedElement;
            let newElement;
            if (updateExisting) {
              existedElement = await DB.Element.findOne({
                where: {
                  name: element.name,
                  pathfinderId: newPathfinder.id,
                }
              })
            }
            let imageUrl = element.image;
            if (imageUrl && !imageUrl.startsWith(AWSHelper.uploadedDomain)) {
              imageUrl = await AWSHelper.uploadFileFromURL({
                url: imageUrl,
                key: VarHelper.genId(),
              })
            }
            let elementData = {
              name: element.name,
              pathfinderId: newPathfinder.id,
              rolloverText: element.rolloverText,
              image: imageUrl,
              filterKeys: element.filters,
            };
            if (existedElement && updateExisting) {
              newElement = await existedElement.update(elementData);
            } else {
              newElement = await DB.Element.create({
                id: VarHelper.genId(),
                clientId,
                ...elementData
              })
            }
            resolve(newElement);
          })
        ));
      }

      // handle statements
      if (pathfinder.statements?.length) {
        await Promise.all(pathfinder.statements.map(statement =>
          new Promise(async (resolve) => {
            const elmNames = statement.element?.split(',');
            const elements = await DB.Element.findAll({
              where: VarHelper.removeUndefinedField({
                pathfinderId: newPathfinder.id,
                name: elmNames ? elmNames.map(i => i?.trim()) : undefined
              })
            })
            let existedStatement;
            let newStatement;
            if (updateExisting) {
              existedStatement = await DB.Statement.findOne({
                where: {
                  statement: statement.statement,
                  pathfinderId: newPathfinder.id,
                }
              })
            }
            const learningIdsObj = {
              learningNamesLikert1: statement.learning1?.split(';').map(i => i?.trim()) || [],
              learningNamesLikert2: statement.learning2?.split(';').map(i => i?.trim()) || [],
              learningNamesLikert3: statement.learning3?.split(';').map(i => i?.trim()) || [],
            };
            const learningsObj = {};
            await Promise.all([1, 2, 3].map(idx =>
              new Promise(async (resv) => {
                const learnings = await Promise.all(
                  learningIdsObj[`learningNamesLikert${idx}`]?.map(name =>
                    new Promise(
                      async (resolve) => {
                        const [learning] = await DB.Learning.findOrCreate({
                          where: { name, clientId },
                          defaults: { name, id: VarHelper.genId(), clientId },
                        });
                        resolve(learning.toJSON());
                      }
                    )
                  )
                );
                learningsObj[idx] = learnings;
                resv(true);
              }))
            );

            const elementIds = elements.filter(el =>
              statement.element?.split(',').map(i => i?.trim()).includes(el.name)
            ).map(el => el.id).join(',');

            const statementData = {
              pathfinderId: newPathfinder.id,
              statement: statement.statement,
              rolloverLikert1: statement.rollover1,
              rolloverLikert2: statement.rollover2,
              rolloverLikert3: statement.rollover3,
              elementIds,
              learningsLikert1: learningsObj[1],
              learningsLikert2: learningsObj[2],
              learningsLikert3: learningsObj[3],
              learningIdsLikert1: learningsObj[1]?.map(i => i.id)?.join(','),
              learningIdsLikert2: learningsObj[2]?.map(i => i.id)?.join(','),
              learningIdsLikert3: learningsObj[3]?.map(i => i.id)?.join(','),
            }
            if (existedStatement && updateExisting) {
              newStatement = await existedStatement.update(statementData);
            } else {
              newStatement = await DB.Statement.create({
                id: VarHelper.genId(),
                clientId,
                ...statementData
              });
            }
            resolve(newStatement);
          })
        ))
      }
    }

    return {
      success: true,
    }
  };
}

export default new ImportPathfinder();
