import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, T<PERSON>ear<PERSON> } from "type";
import { checkA<PERSON>en, combineMiddlewares, validateClientId, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { ERROR } from 'const';
import Joi = require("joi");
import { <PERSON><PERSON><PERSON><PERSON>, VarHelper } from "helpers";

class UpdatePathfinder implements TypeAPIHandler {
  url = "/api/pathfinders/demo-email";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      pathfinderId: Joi.string().required(),
      email: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { pathfinderId, email } = request.body;

    const pathfinder = await DB.Pathfinder.findByPk(pathfinderId);
    if (!pathfinder) throw new Error(ERROR.NOT_EXISTED);

    await validateClientId(request, pathfinder.clientId);

    const learning = await DB.Learning.findOne({
      where: {
        clientId: pathfinder.clientId,
      }
    })

    const replaceData = {
      FirstName: "John",
      PathfinderName: pathfinder.name || "Pathfinder 10",
    }

    const emailContent = `
      ${VarHelper.replacePlaceholders(pathfinder.emailContent || '', replaceData)}
      ${MailHelper.genLearningsTableHtml([learning as TLearning])}
      ${VarHelper.replacePlaceholders(pathfinder.emailFooter || '', replaceData)}
    `;

    const _client = await DB.Client.findOne({
      where: {
        id: pathfinder.clientId
      }
    });

    await MailHelper.sendSMTPEmail({
      sender: _client?.name,
      clientSlug: _client?.slug,
      to: email,
      cc: pathfinder.additionalRecipients ? pathfinder.additionalRecipients.split(',') : [],
      subject: pathfinder.emailSubject,
      html: emailContent,
    })

    return {
      success: true,
    }
  };
}

export default new UpdatePathfinder();
