import { <PERSON>AP<PERSON><PERSON><PERSON><PERSON> } from 'type';
import { validateRequest, combineMiddlewares, checkAuthen, checkAdmin, validateClientId } from '../api-middlewares'
import { DB } from 'db';
import { <PERSON>ar<PERSON>elper } from 'helpers';
import Joi = require("joi");

class CreatePathfinder implements TypeAPIHandler {
  url = '/api/pathfinders/create';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      id: Joi.string().allow(''),
      name: Joi.string().allow(''),
      clientId: Joi.string(),
      logo: Joi.string().allow(''),
      orderIndex: Joi.number(),
      welcomeInstruction: Joi.string().allow(''),
      additionalInstruction: Joi.string().allow(''),
      completionInstruction: Joi.string().allow(''),
      listStatementTitle: Joi.string().allow(''),
      listStatementSubtitle: Joi.string().allow(''),
      listLearningTitle: Joi.string().allow(''),
      likertScaleTitle1: Joi.string().allow(''),
      likertScaleTitle2: Joi.string().allow(''),
      likertScaleTitle3: Joi.string().allow(''),
      elementsTitle: Joi.string().allow(''),
      disableElementsFilter: Joi.boolean(),
      sendEmail: Joi.boolean(),
      additionalRecipients: Joi.string().allow(''),
      emailSubject: Joi.string().allow(''),
      emailContent: Joi.string().allow(''),
      emailFooter: Joi.string().allow(''),
      data: Joi.object(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request, reply) => {
    const {
      name, clientId: cId, logo, data, id,
      orderIndex,
      welcomeInstruction,
      additionalInstruction,
      completionInstruction,
      listStatementTitle,
      listStatementSubtitle,
      listLearningTitle,
      likertScaleTitle1,
      likertScaleTitle2,
      likertScaleTitle3,
      elementsTitle,
      disableElementsFilter,
      sendEmail,
      additionalRecipients,
      emailSubject,
      emailContent,
      emailFooter,
    } = request.body;

    const clientId = await validateClientId(request, cId);

    const client = await DB.Pathfinder.create({
      id: id || VarHelper.genId(),
      clientId,
      name,
      logo,
      orderIndex,
      welcomeInstruction,
      additionalInstruction,
      completionInstruction,
      listStatementTitle,
      listStatementSubtitle,
      listLearningTitle: listLearningTitle || `That’s the career path to be {{.ElementName}}`,
      likertScaleTitle1,
      likertScaleTitle2,
      likertScaleTitle3,
      elementsTitle,
      disableElementsFilter,
      sendEmail,
      additionalRecipients,
      emailSubject,
      emailContent: emailContent || `Congratulations {{.FirstName}} on completing the {{.PathfinderName}} Pathfinder. You can begin learning now by clicking on one of the links below.`,
      emailFooter,
      data,
    });

    return {
      success: true,
      data: client,
    }
  }
}

export default new CreatePathfinder();
