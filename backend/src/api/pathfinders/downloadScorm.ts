import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { checkA<PERSON>en, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import Joi = require("joi");
import { <PERSON><PERSON><PERSON><PERSON>, VarHelper } from "helpers";
import { ERROR } from "const";

class PublishSCORM implements TypeAPIHandler {
  url = "/api/pathfinders/scorm";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      id: Joi.string().required(),
      type: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { id, type } = request.body;

    const pathfinder = await DB.Pathfinder.findByPk(id);
    if (!pathfinder) throw new Error(ERROR.NOT_EXISTED);

    const client = await DB.Client.findByPk(pathfinder.clientId);

    let pathfinderUrl = "";
    if (process.env.WEB_HOST === "pathfinder-rebuild.devserver.london") {
      pathfinderUrl = `https://learner.${process.env.WEB_HOST}/${pathfinder.id}`;
    } else {
      pathfinderUrl = VarHelper.replacePlaceholders(global.frontendPathfinderDomain, {
        slug: client.slug,
        region: client.region,
        pathfinderId: pathfinder.id,
      });
    }

    const output = FileHelper.editTemplateSCORM({
      pathfinderUrl,
      pathfinderName: pathfinder.name,
    },
      type === 'scorm-1.2' ? 'scorm-1.2' : 'scorm-2004',
      pathfinder.name || pathfinder.id,
    )
    let downloadUrl = "";
    if (process.env.WEB_HOST === "pathfinder-rebuild.devserver.london") {
      downloadUrl = `https://api.${process.env.WEB_HOST}/${output}`;
    } else {
      downloadUrl = `${VarHelper.getEndpointByRegion(global.region)}${output}`;
    }

    return {
      success: true,
      data: {
        url: downloadUrl,
      }
    }
  };
}

export default new PublishSCORM();
