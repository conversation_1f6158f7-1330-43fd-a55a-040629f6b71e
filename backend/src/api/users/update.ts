import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { check<PERSON><PERSON>en, combineMiddlewares, validateRequest, validateClientId } from "api/api-middlewares";
import { DB } from "db";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>Helper } from "helpers";
import { ERROR } from 'const';
import Joi = require("joi");

class UpdateAdmin implements TypeAPIHandler {

  url = "/api/users/update";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      id: Joi.string().required(),
      firstName: Joi.string().allow(''),
      lastName: Joi.string().allow(''),
      email: Joi.string().allow(''),
      password: Joi.string().allow(''),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { id, ...restParams } = request.body;

    const user = await DB.User.findByPk(id);
    if (!user) throw new Error(ERROR.EMAIL_NOT_EXISTED);

    if (request.user.role !== 'admin') {
      await validateClientId(request, user.clientId);
    }

    if (restParams.email && restParams.email !== user.email) {
      const existed = await DB.User.findOne({
        where: { email: restParams.email }
      });
      if (existed) throw new Error(ERROR.ACCOUNT_EXISTED);
    }

    if (restParams.password) {
      restParams.password = await AuthenHelper.hashPassword(restParams.password);

      const _client = user.clientId ? (await DB.Client.findOne({
        where: {
          id: user.clientId,
        }
      })) : undefined;
    
      const domain = (() => {
        if (user.role === "admin") {
          return `admin.harborpathfinder.com`; // ${process.env.WEB_HOST} temp
        }
        return `${_client?.slug}.harborpathfinder.com`; // ${process.env.WEB_HOST} temp
      })();
    
      await MailHelper.sendSMTPEmail({
        sender: _client?.name,
        clientSlug: _client?.slug,
        to: user.email,
        subject: 'Your Pathfinder admin password has changed',
        html: MailHelper.genChangePasswordEmail({
          firstName: user.firstName,
          lastName: user.lastName,
          password: request.body.password,
          link: `https://${domain}/forgot-password`,
        }),
      })
    }

    for (let key in restParams) {
      if (restParams[key] !== undefined) {
        user[key] = restParams[key];
      }
    }

    await user.save();

    const publicInfo = Object.assign(JSON.parse(JSON.stringify(user)), {
      password: undefined,
      resetPasswordCode: undefined,
    });

    return {
      success: true,
      data: publicInfo,
    }
  };
}

export default new UpdateAdmin();
