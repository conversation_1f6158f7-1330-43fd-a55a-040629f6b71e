import { TRe<PERSON>User, <PERSON>AP<PERSON><PERSON>and<PERSON> } from 'type';
import { validateRequest, combineMiddlewares, checkAuthen } from '../api-middlewares'
import { ERROR } from 'const';
import { DB } from 'db';
import { <PERSON>then<PERSON><PERSON>per } from 'helpers';
import Joi = require("joi");

class ChangePassword implements TypeAPIHandler {
  url = '/api/users/change-password';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      oldPassword: Joi.string().required(),
      newPassword: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser) => {
    const { oldPassword, newPassword } = request.body;

    const user = await DB.User.findByPk(request.user.id);
    if (!user) throw new Error(ERROR.EMAIL_NOT_EXISTED);

    const isPasswordCorrect = await AuthenHelper.comparePassword(oldPassword, user.password);
    if (!isPasswordCorrect) throw new Error(ERROR.WRONG_PASSWORD);

    user.password = await AuthenHelper.hashPassword(newPassword);
    await user.save();

    return {
      success: true,
    }
  }
}

export default new ChangePassword();
