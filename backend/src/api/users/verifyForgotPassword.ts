import { <PERSON>AP<PERSON>H<PERSON><PERSON> } from 'type';
import { validateRequest, combineMiddlewares } from '../api-middlewares'
import { ERROR } from 'const';
import { DB } from 'db';
import Joi = require("joi");

class VerifyForgotPassword implements TypeAPIHandler {
  url = '/api/users/verify-forgot-password';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      code: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
  ]);

  handler = async (request) => {
    const { code } = request.body;

    const user = await DB.User.findOne({
      where: {
        resetPasswordCode: code,
      }
    });

    if (!user) throw new Error(ERROR.EMAIL_NOT_EXISTED);
    return {
      success: true,
    }
  }
}

export default new VerifyForgotPassword();
