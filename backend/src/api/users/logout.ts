import { <PERSON>AP<PERSON><PERSON>and<PERSON> } from 'type';
import { checkAuthen, combineMiddlewares } from '../api-middlewares';
import { InMemory as RedisCache } from 'db';

class Logout implements TypeAPIHandler {
  url = '/api/users/logout';
  method = 'POST';

  preHandler = combineMiddlewares([
    checkA<PERSON>en,
  ]);

  handler = async (request, reply) => {
    const { id } = request.user;
    const token = request.headers['Authorization'] || request.headers['authorization'];
    await RedisCache.hdelAsync('JWT', token);
    const listToken = await RedisCache.hgetAsync('JWTs', id);
    if (listToken) {
      const parsedList = JSON.parse(listToken);
      await RedisCache.hsetAsync('JWTs', id, JSON.stringify(parsedList.filter(val => val !== token)));
    }
    return { success: true };
  }
}

export default new Logout();
