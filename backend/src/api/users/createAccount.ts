import { MULTI_FACTOR_TYPE, TypeAPIHandler } from 'type';
import { validateRequest, combineMiddlewares, checkAuthen } from '../api-middlewares'
import { ERROR } from 'const';
import { DB } from 'db';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'helpers';
import Joi = require("joi");

class CreateAccount implements TypeAPIHandler {
  url = '/api/users/create-account';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      email: Joi.string().required(),
      password: Joi.string().required(),
      firstName: Joi.string().required(),
      lastName: Joi.string().allow(''),
      role: Joi.string().required(),
      clientId: Joi.string().allow(null),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request, reply) => {
    const { role, email, password, firstName, lastName, clientId: cId } = request.body;

    const find = await DB.User.findOne({
      where: { email: email.toLowerCase() },
    });
    if (find) throw new Error(ERROR.ACCOUNT_EXISTED);

    let clientId = cId;
    const isGlobalAdmin = request.user?.role === 'admin';

    if (isGlobalAdmin) {
      if (!cId && role !== 'admin') throw new Error(ERROR.CLIENT_ID_REQUIRED);
    }
    if (!isGlobalAdmin) {
      if (cId && cId !== request.user.clientId) {
        throw new Error(ERROR.PERMISSION_DENIED);
      } else {
        clientId = request.user.clientId;
      }
    }
    if (clientId) {
      const client = await DB.Client.findByPk(clientId);
      if (!client) throw new Error(ERROR.CLIENT_NOT_EXISTED);
    }

    const hashedPassword = await AuthenHelper.hashPassword(password);

    const user = await DB.User.create({
      id: VarHelper.genId(),
      email: email.toLowerCase(),
      password: hashedPassword as string,
      firstName, lastName,
      role,
      clientId,
      multiFactorType: [ MULTI_FACTOR_TYPE.NONE ],
    });

    const data = Object.assign(JSON.parse(JSON.stringify(user)), {
      password: undefined,
      resetPasswordCode: undefined,
    });

    return {
      success: true,
      data,
    }
  }
}

export default new CreateAccount();
