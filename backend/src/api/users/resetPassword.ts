import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'type';
import { validateRequest, combineMiddlewares } from '../api-middlewares'
import { ERROR } from 'const';
import { DB } from 'db';
import { Authen<PERSON>elper } from 'helpers';
import Joi = require("joi");

class ResetPassword implements TypeAPIHandler {
  url = '/api/users/reset-password';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      code: Joi.string().required(),
      newPassword: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
  ]);

  handler = async (request) => {
    const { code, newPassword } = request.body;

    const user = await DB.User.findOne({
      where: {
        resetPasswordCode: code,
      }
    });
    if (!user) throw new Error(ERROR.EMAIL_NOT_EXISTED);

    user.password = await AuthenHelper.hashPassword(newPassword);
    user.resetPasswordCode = "";
    await user.save();

    return {
      success: true,
    }
  }
}

export default new ResetPassword();
