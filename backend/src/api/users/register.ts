import { <PERSON>AP<PERSON><PERSON><PERSON><PERSON> } from 'type';
import { validateRequest, combineMiddlewares } from '../api-middlewares'
import { ERROR } from 'const';
import { DB } from 'db';
import { <PERSON>the<PERSON><PERSON><PERSON><PERSON>, Var<PERSON>elper } from 'helpers';
import Joi = require("joi");

class Register implements TypeAPIHandler {
  url = '/api/users/register';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      email: Joi.string().required(),
      password: Joi.string().required(),
      firstName: Joi.string().required(),
      lastName: Joi.string().allow(''),
      role: Joi.string().required(),
      photoUrl: Joi.string().allow(''),
      addressLine1: Joi.string().allow(''),
      addressLine2: Joi.string().allow(''),
      town: Joi.string().allow(''),
      country: Joi.string().allow(''),
      postCode: Joi.string().allow(''),
      otherData: Joi.any(),
      __key: Joi.string(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
  ]);

  handler = async (request, reply) => {
    const { role, __key, email, password, firstName, lastName, photoUrl, otherData, addressLine1, addressLine2, town, country, postCode } = request.body;
    if (role === 'admin' && __key !== '3njkdnf231&*(*adbf') {
      throw new Error(ERROR.PERMISSION_DENIED);
    }
    const hashedPassword = await AuthenHelper.hashPassword(password);
    const user = await DB.User.create({
      id: VarHelper.genId(),
      email: email.toLowerCase(),
      password: hashedPassword as string,
      firstName, lastName, photoUrl, otherData,
      role,
      addressLine1, addressLine2, town, country, postCode
    });

    const token = AuthenHelper.genJWTToken({ id: user.id });
    const publicInfo = Object.assign(JSON.parse(JSON.stringify(user)), {
      password: undefined,
      resetPasswordCode: undefined,
    });

    return {
      success: true,
      data: {
        token,
        publicInfo,
      }
    }
  }
}

export default new Register();
