import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "helpers";
import { <PERSON><PERSON><PERSON>Hand<PERSON> } from "type";
import { combineMiddlewares, checkAuthen } from "../api-middlewares";
import Joi = require("joi");

class UploadImageUrl implements TypeAPIHandler {
  url = "/api/users/upload-image-url";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      url: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    checkAuthen,
  ]);

  handler = async (request, reply) => {
    const { url } = request.body;
    const res = await AWSHelper.uploadFileFromURL({
      url,
      key: VarHelper.genId(),
    });
    return { success: true, data: res };
  };
}

export default new UploadImageUrl();
