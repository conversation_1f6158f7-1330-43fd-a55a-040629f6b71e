import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'type';
import { validateRequest, combineMiddlewares } from '../api-middlewares'
import { ERROR } from 'const';
import { DB } from 'db';
import { <PERSON><PERSON><PERSON><PERSON>, VarHelper } from 'helpers';
import Joi = require("joi");

class ForgotPassword implements TypeAPIHandler {
  url = '/api/users/forgot-password';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      email: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
  ]);

  handler = async (request) => {
    const { email } = request.body;

    const user = await DB.User.findOne({
      where: {
        email,
      }
    });
    if (!user) throw new Error(ERROR.NOT_EXISTED);

    const resetCode = VarHelper.genShortId(10);
    user.resetPasswordCode = resetCode;

    await user.save();

    const _client = user.clientId ? (await DB.Client.findOne({
      where: {
        id: user.clientId,
      }
    })) : undefined;

    const domain = (() => {
      if (user.role === "admin") {
        return `admin.harborpathfinder.com`; // ${process.env.WEB_HOST} temp
      }
      return `${_client?.slug}.harborpathfinder.com`; // ${process.env.WEB_HOST} temp
    })();

    await MailHelper.sendSMTPEmail({
      sender: _client?.name,
      clientSlug: _client?.slug,
      to: user.email,
      subject: 'Pathfinder Password Reset',
      html: MailHelper.genForgotPasswordEmail({
        firstName: user.firstName,
        lastName: user.lastName,
        link: `${domain}/reset-password?code=${resetCode}`,
      }),
    })

    return {
      success: true,
    }
  }
}

export default new ForgotPassword();
