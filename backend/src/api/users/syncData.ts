import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { combineMiddlewares } from "api/api-middlewares";
import { DB } from "db";
import Joi = require("joi");
import { checkServerRequest } from "api/api-middlewares/authen";

class SyncUser implements TypeAPIHandler {
  url = "/api/users/sync";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      id: Joi.string().allow(''),
      email: Joi.string().allow(''),
      password: Joi.string().allow(''),
      firstName: Joi.string().allow(),
      lastName: Joi.string().allow(''),
      role: Joi.string().allow(''),
      clientId: Joi.string().allow(''),
    }),
  }

  preHandler = combineMiddlewares([
    checkServerRequest,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const params = request.body;

    const user = await DB.User.findByPk(params.id);

    if (!user) {
      const userByEmail = await DB.User.findOne({
        where: {
          email: params.email,
        }
      });
      if (userByEmail) {
        for (let key in params) {
          userByEmail[key] = params[key];
        }
        await userByEmail.save();
      } else {
        await DB.User.create(params);
      }
    } else {
      for (let key in params) {
        user[key] = params[key];
      }
      await user.save();
    }

    return {
      success: true,
    }
  };
}

export default new SyncUser();
