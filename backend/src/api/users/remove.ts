import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { checkAuthen, combineMiddlewares, validateRequest, validateClientId } from "api/api-middlewares";
import { DB } from "db";
import { ERROR } from 'const';
import Joi = require("joi");

class RemoveUser implements TypeAPIHandler {
  url = "/api/users/remove";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      id: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { id } = request.body;

    const user = await DB.User.findByPk(id);
    if (!user) throw new Error(ERROR.EMAIL_NOT_EXISTED);

    if (request.user.role !== 'admin') {
      await validateClientId(request, user.clientId);
    }

    await user.destroy();

    return {
      success: true,
    }
  };
}

export default new RemoveUser();
