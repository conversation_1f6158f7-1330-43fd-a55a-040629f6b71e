import { MULTI_FACTOR_TYPE, TypeAP<PERSON><PERSON><PERSON><PERSON> } from "type";
import { validateRequest, combineMiddlewares } from "../api-middlewares";
import { ERROR } from "const";
import { DB, InMemory as RedisCache } from "db";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "helpers";
import Joi = require("joi");
import { getAuth } from "firebase-admin/auth";
import { parse } from "postgres-array";

class VerifyToken implements TypeAPIHandler {
  url = "/api/users/verify-token";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      userId: Joi.string().required(),
      idToken: Joi.string().required(),
      isLogin: Joi.boolean(),
    }),
  };

  preHandler = combineMiddlewares([validateRequest(this.apiSchema)]);

  handler = async (request, reply) => {
    const { userId, idToken, isLogin } = request.body;
    let user = await DB.User.findOne({
      where: { id: userId },
    });
    if (!user) throw new Error(ERROR.USER_NOT_FOUND);
    let data;
    const decodedToken = await getAuth().verifyIdToken(idToken);
    if (!decodedToken) throw new Error(ERROR.VERIFY_TOKEN_FAIL);
    if (decodedToken && !isLogin) {
      let multiFactorTypeArray = parse(user.multiFactorType as string);
      if (multiFactorTypeArray.includes('NONE')) {
        multiFactorTypeArray = multiFactorTypeArray.filter(item => item !== 'NONE');
      }

      multiFactorTypeArray.push(MULTI_FACTOR_TYPE.PHONE);
      await DB.User.update(
        {
          phoneNumber: decodedToken.phone_number,
          multiFactorType: multiFactorTypeArray,
        },
        {
          where: {
            id: userId,
          },
        }
      );
      user = await DB.User.findOne({
        where: { id: userId },
      });
    }
    const token = AuthenHelper.genJWTToken({ id: user.id });
    const publicInfo = Object.assign(JSON.parse(JSON.stringify(user)), {
      password: undefined,
      resetPasswordCode: undefined,
      secret: undefined,
    });
    _afterLogin(publicInfo, token);

    data = {
      success: true,
      data: {
        token,
        publicInfo: {
          ...publicInfo,
          multiFactorType: parse(user.multiFactorType as string),
        },
      },
    };

    return data;
  };
}

async function _afterLogin(user, token) {
  const listToken = await RedisCache.hgetAsync("JWTs", user.id);
  if (listToken) {
    const parsedList = JSON.parse(listToken);
    if (!parsedList.includes(token)) {
      parsedList.push(token);
      await RedisCache.hsetAsync("JWTs", user.id, JSON.stringify(parsedList));
    }
  } else {
    await RedisCache.hsetAsync("JWTs", user.id, JSON.stringify([token]));
  }
  await RedisCache.hsetAsync("JWT", token, JSON.stringify(user));
}

export default new VerifyToken();
