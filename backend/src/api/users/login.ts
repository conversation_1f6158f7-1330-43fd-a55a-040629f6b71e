import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "type";
import { validateRequest, combineMiddlewares } from "../api-middlewares";
import { ERROR } from "const";
import { DB, InMemory as RedisCache } from "db";
import { <PERSON>then<PERSON><PERSON><PERSON> } from "helpers";
import Joi = require("joi");
import { MULTI_FACTOR_TYPE } from "../../../../api-client";
import { parse } from 'postgres-array';

class Login implements TypeAPIHandler {
  url = "/api/users/login";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      email: Joi.string().required(),
      password: Joi.string().required(),
    }),
  };

  preHandler = combineMiddlewares([validateRequest(this.apiSchema)]);

  handler = async (request, reply) => {
    const { email, password } = request.body;
    const user = await DB.User.findOne({
      where: { email: email.toLowerCase() },
      raw: true,
    });
    if (!user) throw new Error(ERROR.EMAIL_NOT_EXISTED);
    const isPasswordCorrect = await AuthenHelper.comparePassword(
      password,
      user.password
    );
    if (!isPasswordCorrect) throw new Error(ERROR.EMAIL_NOT_EXISTED);
    const publicInfo = Object.assign(JSON.parse(JSON.stringify(user)), {
      password: undefined,
      resetPasswordCode: undefined,
      secret: undefined,
    });

    if (
      user?.multiFactorType &&
      !publicInfo?.multiFactorType.includes(MULTI_FACTOR_TYPE.NONE)
    ) {
      return {
        success: true,
        isHaveMultiFactor: true,
        data: {
          id: publicInfo.id,
          multiFactorType: parse(publicInfo?.multiFactorType as string),
          ...(user?.phoneNumber && { phoneNumber: user?.phoneNumber }),
        },
      };
    } else {
      const token = AuthenHelper.genJWTToken({ id: user.id });
      _afterLogin(publicInfo, token);

      return {
        success: true,
        isHaveMultiFactor: false,
        data: {
          token,
          publicInfo: {
            ...publicInfo,
            multiFactorType: publicInfo?.multiFactorType ? parse(publicInfo?.multiFactorType as string) : ['NONE'],
          }
        },
      };
    }
  };
}

async function _afterLogin(user, token) {
  const listToken = await RedisCache.hgetAsync("JWTs", user.id);
  if (listToken) {
    const parsedList = JSON.parse(listToken);
    if (!parsedList.includes(token)) {
      parsedList.push(token);
      await RedisCache.hsetAsync("JWTs", user.id, JSON.stringify(parsedList));
    }
  } else {
    await RedisCache.hsetAsync("JWTs", user.id, JSON.stringify([token]));
  }
  await RedisCache.hsetAsync("JWT", token, JSON.stringify(user));
}

export default new Login();
