import { MULTI_FACTOR_TYPE, TypeAP<PERSON><PERSON><PERSON><PERSON> } from "type";
import { validateRequest, combineMiddlewares } from "../../api-middlewares";
import { ERROR } from "const";
import { DB, InMemory as RedisCache } from "db";
import { <PERSON>the<PERSON><PERSON><PERSON><PERSON> } from "helpers";
import Joi = require("joi");
import { getAuth } from "firebase-admin/auth";
import { generateOTPToken, generateQRCode, generateUniqueSecret } from "helpers/2fa";

class Enable2FA implements TypeAPIHandler {
  url = "/api/users/enable2FA";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      userId: Joi.string().required(),
    }),
  };

  preHandler = combineMiddlewares([validateRequest(this.apiSchema)]);

  handler = async (request, reply) => {
    const { userId, idToken, isLogin } = request.body;
    let user = await DB.User.findOne({
      where: { id: userId },
    });
    if (!user) throw new Error(ERROR.USER_NOT_FOUND);
    let userSecret = user?.secret;
    if (!userSecret) {
      const secret = generateUniqueSecret();
      await DB.User.update(
        {
          secret: secret,
        },
        {
          where: {
            id: userId,
          },
        }
      );
      userSecret = secret;
    }

    const serviceName ="Pathfinder";
    const otpAuth = generateOTPToken(user.email, serviceName, userSecret);
    
    const QRCodeImage = await generateQRCode(otpAuth);
    return {
      success: true,
      data: {
        QRCodeImage,
      },
    };
  };
}

export default new Enable2FA();
