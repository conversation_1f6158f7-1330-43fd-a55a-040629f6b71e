import { MULTI_FACTOR_TYPE, TypeAP<PERSON><PERSON>and<PERSON> } from "type";
import { validateRequest, combineMiddlewares } from "../../api-middlewares";
import { ERROR } from "const";
import { DB, InMemory as RedisCache } from "db";
import { <PERSON>the<PERSON><PERSON><PERSON><PERSON> } from "helpers";
import Joi = require("joi");
import { verifyOTPToken } from "helpers/2fa";
import { parse } from "postgres-array";

class Verify2FA implements TypeAPIHandler {
  url = "/api/users/verify2FA";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      userId: Joi.string().required(),
      token: Joi.string().required(),
      isLogin: Joi.boolean(),
    }),
  };

  preHandler = combineMiddlewares([validateRequest(this.apiSchema)]);

  handler = async (request, reply) => {
    const { userId, token, isLogin } = request.body;
    let user = await DB.User.findOne({
      where: { id: userId },
    });
    if (!user) throw new Error(ERROR.USER_NOT_FOUND);
    if (!user.secret) throw new Error(ERROR.USER_SECRET_NOT_FOUND);
    const isValid = verifyOTPToken(token, user.secret);

    if (!isValid) throw new Error(ERROR.VERIFY_TOKEN_FAIL);
    if (isValid && !isLogin) {
      let multiFactorTypeArray = parse(user.multiFactorType as string);
      if (multiFactorTypeArray.includes('NONE')) {
        multiFactorTypeArray = multiFactorTypeArray.filter(item => item !== 'NONE');
      }

      multiFactorTypeArray.push('TOTP');
      await DB.User.update(
        {
          multiFactorType: multiFactorTypeArray,
        },
        {
          where: {
            id: userId,
          },
        }
      );
      user = await DB.User.findOne({
        where: { id: userId },
      });
    }
    const userToken = AuthenHelper.genJWTToken({ id: user.id });
    const publicInfo = Object.assign(JSON.parse(JSON.stringify(user)), {
      password: undefined,
      resetPasswordCode: undefined,
      secret: undefined,
    });
    _afterLogin(publicInfo, userToken);

    return {
      success: true,
      data: {
        token: userToken,
        publicInfo: {
          ...publicInfo,
          multiFactorType: parse(user.multiFactorType as string),
        },
      },
    };
  };
}

async function _afterLogin(user, token) {
  const listToken = await RedisCache.hgetAsync("JWTs", user.id);
  if (listToken) {
    const parsedList = JSON.parse(listToken);
    if (!parsedList.includes(token)) {
      parsedList.push(token);
      await RedisCache.hsetAsync("JWTs", user.id, JSON.stringify(parsedList));
    }
  } else {
    await RedisCache.hsetAsync("JWTs", user.id, JSON.stringify([token]));
  }
  await RedisCache.hsetAsync("JWT", token, JSON.stringify(user));
}

export default new Verify2FA();
