import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { combineMiddlewares, checkAuthen, validateRequest, checkAdmin, validateClientId } from "api/api-middlewares";
import { DB } from "db";
import Joi = require("joi");
import { Op } from "sequelize";
import { ERROR } from "const";

class UserList implements TypeAPIHandler {

  url = "/api/users";
  method = "GET";
  apiSchema = {
    query: Joi.object({
      clientId: Joi.string().required(),
      allClient: Joi.bool(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { clientId: cId, allClient } = request.query;

    let clientId = cId;
    if (request.user.role !== 'admin') {
      clientId = await validateClientId(request, cId);
    }

    if (allClient && request.user.role !== 'admin') {
      throw new Error(ERROR.PERMISSION_DENIED);
    }

    const users = await DB.User.findAll({
      where: allClient ? {
        clientId: { [Op.not]: null }
      } : {
        clientId: clientId !== 'null'
          ? clientId
          : { [Op.eq]: null }
      },
      order: [
        ['createdAt', 'DESC'],
      ],
    });

    const publicInfos = users.map(user => Object.assign(JSON.parse(JSON.stringify(user)), {
      password: undefined,
      resetPasswordCode: undefined,
    }))

    return {
      success: true,
      data: publicInfos,
    }
  };
}

export default new UserList();
