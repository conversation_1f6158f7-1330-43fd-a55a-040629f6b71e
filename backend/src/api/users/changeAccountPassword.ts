import { TRe<PERSON>User, <PERSON>AP<PERSON><PERSON><PERSON><PERSON> } from 'type';
import { validateRequest, combineMiddlewares, checkAuthen, checkAdmin } from '../api-middlewares'
import { ERROR } from 'const';
import { DB } from 'db';
import { <PERSON>the<PERSON><PERSON><PERSON><PERSON> } from 'helpers';
import Joi = require("joi");

class ChangeAccountPassword implements TypeAPIHandler {
  url = '/api/users/change-account-password';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      id: Joi.string().required(),
      password: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
    checkAdmin,
  ]);

  handler = async (request: TRequestUser) => {
    const { password, id } = request.body;

    const user = await DB.User.findByPk(id);
    if (!user) throw new Error(ERROR.EMAIL_NOT_EXISTED);

    user.password = await <PERSON>then<PERSON>elper.hashPassword(password);
    await user.save();

    return {
      success: true,
    }
  }
}

export default new ChangeAccountPassword();
