import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { check<PERSON><PERSON><PERSON>, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { ERROR } from 'const';
import Joi = require("joi");

class MaintainerImport implements TypeAPIHandler {
  url = "/api/maintainer/import-all";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      type: Joi.string(),
      data: Joi.any(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { type, data } = request.body;
    if (request.headers['admin-secret'] !== "CD%2024-dev" && request.headers['Admin-Secret'] !== "CD%2024-dev") throw new Error(ERROR.PERMISSION_DENIED);
    if (type === "clients") {
      await Promise.all(data.map(async item => {
        const existed = await DB.Client.findByPk(item.id);
        if (!existed) {
          await DB.Client.create(item);
        }
      }))
    }
    if (type === "learnings") {
      await Promise.all(data.map(async item => {
        const existed = await DB.Learning.findByPk(item.id);
        if (!existed) {
          await DB.Learning.create(item);
        }
      }))
    }
    if (type === "pathfinders") {
      await Promise.all(data.map(async item => {
        const existed = await DB.Pathfinder.findByPk(item.id);
        if (!existed) {
          await DB.Pathfinder.create(item);
        }
      }))
    }
    if (type === "elements") {
      await Promise.all(data.map(async item => {
        const existed = await DB.Element.findByPk(item.id);
        if (!existed) {
          await DB.Element.create(item);
        }
      }))
    }
    if (type === "statements") {
      await Promise.all(data.map(async item => {
        const existed = await DB.Statement.findByPk(item.id);
        if (!existed) {
          await DB.Statement.create(item);
        }
      }))
    }

    return {
      success: true,
    }
  };
}

export default new MaintainerImport();
