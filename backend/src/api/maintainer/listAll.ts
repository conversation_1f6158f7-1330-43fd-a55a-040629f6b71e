import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { check<PERSON><PERSON><PERSON>, combineMiddlewares } from "api/api-middlewares";
import { DB } from "db";
import Joi = require("joi");
import { ERROR } from "const";

class MaintainerListAll implements TypeAPIHandler {

  url = "/api/maintainer/list-all";
  method = "GET";
  apiSchema = {
    query: Joi.object({
      type: Joi.string(),
    })
  }

  preHandler = combineMiddlewares([
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { type } = request.query;
    if (request.headers['admin-secret'] !== "CD%2024-dev" && request.headers['Admin-Secret'] !== "CD%2024-dev") throw new Error(ERROR.PERMISSION_DENIED);

    let data = [];
    if (type === "clients") {
      data = await DB.Client.findAll();
    }
    if (type === "learnings") {
      data = await DB.Learning.findAll();
    }
    if (type === "pathfinders") {
      data = await DB.Pathfinder.findAll();
    }
    if (type === "elements") {
      data = await DB.Element.findAll();
    }
    if (type === "statements") {
      data = await DB.Statement.findAll();
    }

    return {
      success: true,
      data,
    }
  };
}

export default new MaintainerListAll();
