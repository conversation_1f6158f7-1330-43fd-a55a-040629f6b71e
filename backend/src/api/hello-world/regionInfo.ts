import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "helpers";
import { TypeAPIHandler } from "type";

class RegionInfo implements TypeAPIHandler {
  url = "/b/api/regionInfo";
  method = "GET";

  handler = async (request, reply) => {
    return {
      hello: "world",
      region: global.region,
      thisServer: global.isMainServer ? global.mainRegionEndpoint : global.subRegionEndpoint,
    };
  };
}

export default new RegionInfo();
