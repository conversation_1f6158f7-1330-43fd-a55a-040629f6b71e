import { <PERSON>AP<PERSON><PERSON><PERSON><PERSON> } from "type";
import {
  receiveFileAndFields,
  combineMiddlewares,
  IModifiedBody,
} from "../api-middlewares";
import { Logger } from "helpers";

class SamplePost implements TypeAPIHandler {
  url = "/b/api/test/form-body";
  method = "POST";

  preHandler = combineMiddlewares([receiveFileAndFields]);

  handler = async (request, reply) => {
    const body: IModifiedBody = request.body;
    Logger.remoteLog(this.url, request.headers["content-type"], body);
    return {
      ...body,
    };
  };
}

export default new SamplePost();
