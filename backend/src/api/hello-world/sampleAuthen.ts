import { <PERSON>AP<PERSON><PERSON><PERSON><PERSON> } from "type";
import { combineMiddlewares, checkAuthen } from "../api-middlewares";

class HelloWorld implements TypeAPIHandler {
  url = "/b/api/test-authen";
  method = "GET";

  preHandler = combineMiddlewares([
    checkAuthen, // if authen success, it will add user to request
  ]);

  handler = async (request, reply) => {
    return { hello: "world4", user: request.user };
  };
}

export default new HelloWorld();
