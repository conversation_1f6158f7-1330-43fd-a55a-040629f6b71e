import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { checkAuthen, combineMiddlewares, validateRequest, validateClientId } from "api/api-middlewares";
import { DB } from "db";
import { ERROR } from 'const';
import Joi = require("joi");

class RemoveElement implements TypeAPIHandler {
  url = "/api/elements/remove";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      id: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { id } = request.body;

    const element = await DB.Element.findByPk(id);
    if (!element) throw new Error(ERROR.NOT_EXISTED);

    if (request.user.role !== 'admin') {
      await validateClientId(request, element.clientId);
    }

    await element.destroy();

    return {
      success: true,
    }
  };
}

export default new RemoveElement();
