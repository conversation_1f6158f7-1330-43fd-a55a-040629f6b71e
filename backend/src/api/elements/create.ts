import { <PERSON>AP<PERSON><PERSON><PERSON><PERSON> } from 'type';
import { validateRequest, combineMiddlewares, checkAuthen, validateClientId } from '../api-middlewares'
import { DB } from 'db';
import { VarHelper } from 'helpers';
import Joi = require("joi");

class CreateElement implements TypeAPIHandler {
  url = '/api/elements/create';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      id: Joi.string().allow(''),
      name: Joi.string().required(),
      pathfinderId: Joi.string().required(),
      clientId: Joi.string().allow(''),
      rolloverText: Joi.string().allow(''),
      image: Joi.string().allow(''),
      filterKeys: Joi.string().allow(''),
      orderIndex: Joi.number(),
      data: Joi.object(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request, reply) => {
    const { id, name, clientId: cId, pathfinderId, rolloverText, image, filterKeys, orderIndex, data } = request.body;

    const clientId = await validateClientId(request, cId);

    const element = await DB.Element.create({
      id: id || VarHelper.genId(),
      clientId,
      name,
      pathfinderId,
      rolloverText,
      image,
      filterKeys,
      orderIndex,
      data,
    });

    return {
      success: true,
      data: element,
    }
  }
}

export default new CreateElement();
