import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { checkAuthenOptional, combineMiddlewares } from "api/api-middlewares";
import { DB } from "db";
import { VarHelper } from "helpers";
import Joi = require("joi");
import { ERROR } from "const";

const parseKeysToArr = (str: string) => {
  if (!str) return [];
  return str.split(',').map(i => String(i).trim().toLowerCase())
}

class ElementList implements TypeAPIHandler {

  url = "/api/elements";
  method = "GET";
  apiSchema = {
    query: Joi.object({
      pathfinderId: Joi.string().required(),
      name: Joi.string(),
      rolloverText: Joi.string(),
      filterKeys: Joi.string(),
      all: Joi.boolean(),
    })
  }

  preHandler = combineMiddlewares([
    checkAuthenOptional,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { pathfinderId, name, rolloverText, filterKeys, all } = request.query;

    if (!pathfinderId) throw new Error(ERROR.PATHFINDER_ID_REQUIRED)

    const pathfinder = await DB.Pathfinder.findByPk(pathfinderId);
    if (!pathfinder) throw new Error(ERROR.NOT_EXISTED)

    const includeDeleted = all && ['client', 'admin'].includes(request.user.role);

    let list = await DB.Element.findAll({
      where: VarHelper.removeUndefinedField({
        pathfinderId,
        name,
        rolloverText,
      }),
      order: [
        ['orderIndex', 'ASC'],
        ['createdAt', 'ASC'],
      ],
      paranoid: includeDeleted ? false : true,
    });

    if (filterKeys) {
      list = list.filter(i =>
        i.filterKeys && !parseKeysToArr(filterKeys).some((key) =>
          !parseKeysToArr(i.filterKeys).includes(key)
        )
      )
    }

    return {
      success: true,
      data: list,
    }
  };
}

export default new ElementList();
