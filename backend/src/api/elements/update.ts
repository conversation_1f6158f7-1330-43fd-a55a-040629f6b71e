import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { check<PERSON><PERSON><PERSON>, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { ERROR } from 'const';
import Joi = require("joi");

class UpdateElement implements TypeAPIHandler {
  url = "/api/elements/update";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      id: Joi.string().required(),
      name: Joi.string().allow(''),
      rolloverText: Joi.string().allow(''),
      image: Joi.string().allow(''),
      filterKeys: Joi.string().allow(''),
      orderIndex: Joi.number(),
      data: Joi.object(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { id, ...restParams } = request.body;

    const element = await DB.Element.findByPk(id);
    if (!element) throw new Error(ERROR.NOT_EXISTED);

    for (let key in restParams) {
      if (restParams[key] !== undefined) {
        element[key] = restParams[key];
      }
    }
    await element.save();

    return {
      success: true,
      data: element,
    }
  };
}

export default new UpdateElement();
