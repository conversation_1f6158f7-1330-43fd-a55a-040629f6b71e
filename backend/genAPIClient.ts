const fs = require('fs');
const fse = require('fs-extra');
const path = require('path');
import { convertSchema } from 'joi-to-typescript';

const TAB = '  ';

const capitalize = s => {
  return s[0].toUpperCase() + s.slice(1, s.length);
}

const genInterfaceName = (methodName, suffix) => {
  if (typeof methodName !== 'string' || methodName.length === 0) return (methodName + suffix);
  return 'I' + capitalize(methodName) + suffix;
}

const apiSchemaToTS = (apiSchema, methodName) => {
  const result = {
    tsInterfaces: '',
    body: '',
    query: '',
    params: '',
  }
  if (!apiSchema) return result;
  let { body, params, query } = apiSchema;


  if (body) {
    result.body = genInterfaceName(methodName, 'RequestBody')
    body = body.meta({ className: result.body });
    result.tsInterfaces += '\n' + convertSchema({}, body).content;
  }
  if (params) {
    result.params = genInterfaceName(methodName, 'RequestParams')
    params = params.meta({ className: result.params });
    result.tsInterfaces += '\n' + convertSchema({}, params).content;
  }
  if (query) {
    result.query = genInterfaceName(methodName, 'RequestQuery')
    query = query.meta({ className: result.query });
    result.tsInterfaces += '\n' + convertSchema({}, query).content;
  }

  // const bodyInterface = !body ? '' : convertSchema({}, body).content;
  // const paramsInterface = !params ? '' : convertSchema({}, params).content;
  // const queryInterface = !query ? '' : convertSchema({}, query).content;

  return result;
}

export const genAPIClients = (arrayAPIs) => {
  if (!Array.isArray(arrayAPIs)) return;
  
  // empty the folder api-client/api
  const apiFolder = path.join(__dirname, `../api-client/api`);
  const files = fs.readdirSync(apiFolder);
  files.forEach(f => {
    fs.unlinkSync(path.join(apiFolder, f));
  })
  
  let codeFiles = [];
  for (let i = 0; i < arrayAPIs.length; i++) {
    const { name, apis : ApiObject } =  arrayAPIs[i];
    
    let codeContentString = `import Request from '../Request.utils'`;
    const methodList = [];

    for (let method in ApiObject) {
      const { tsInterfaces, body, params, query } = apiSchemaToTS(ApiObject[method].apiSchema, method)
      codeContentString += tsInterfaces;
      methodList.push({
        obj: ApiObject[method],
        name: method,
        ts: { tsInterfaces, body, params, query },
      })
    }

    codeContentString += '\n\n';

    const className = `${capitalize(name)}API`;
    codeContentString += '\n' + `export class ${className} {`;

    codeContentString += '\n\n';
    codeContentString += '\n' + TAB + `request = Request // for ts auto suggestion`;
    codeContentString += '\n' + TAB + `debugMode = false // for ts auto suggestion`;
    codeContentString += '\n' + TAB + `constructor(request, debugMode = false) {`;
    codeContentString += '\n' + TAB + TAB + `if(request) this.request = request;`;
    codeContentString += '\n' + TAB + TAB + `this.debugMode = debugMode;`;
    codeContentString += '\n' + TAB + `}`;

    methodList.forEach(m => {
      const { argumentString, parametersString } = (() => {
        let tsI = '';
        let p = '';
        if (m.ts.params) {
          tsI += tsI.length > 0 ? ', ' : '';
          tsI += `params: ${m.ts.params}`;
          p += 'params, '
        } else {
          p += 'undefined, '
        }

        if (m.ts.query) {
          tsI += tsI.length > 0 ? ', ' : '';
          tsI += `query: ${m.ts.query}`;
          p += 'query, '
        } else {
          p += 'undefined, '
        }

        if (m.ts.body) {
          tsI += tsI.length > 0 ? ', ' : '';
          tsI += `body: ${m.ts.body}`;
          p += 'body, ';
        } else {
          p += 'undefined, '
        };

        return {
          argumentString: tsI,
          parametersString: p
        }
      })();
      const specificHandle = generateSpecificHandleCode(m);
      codeContentString += '\n' + TAB + `${m.name} = async (${argumentString}) => {`;
      codeContentString += '\n' + TAB + TAB + `if(this.debugMode) console.log('${m.obj.url}', '${m.obj.method}', ${parametersString});`;
      codeContentString += '\n' + TAB + TAB + `const res = await this.request.call('${m.obj.url}', '${m.obj.method}', ${parametersString});${specificHandle}`;
      codeContentString += `\n` + TAB + TAB + `if(this.debugMode) console.log('res.data', res.data);`;
      codeContentString += `\n` + TAB + TAB + `return res;`;
      codeContentString += `\n` + TAB + `}`;
    })

    codeContentString += '\n' + '}'
    codeContentString += '\n' + `export default new ${className}(Request);`

    const filePath = path.join(__dirname, `../api-client/api/${className}.ts`);
    codeFiles.push(className);
    fs.writeFileSync(filePath, codeContentString);
  }

  

  const indexPath = path.join(__dirname, `../api-client/api/index.ts`);
  const indexClassPath = path.join(__dirname, `../api-client/api/index.class.ts`);
  fs.writeFileSync(indexPath, codeFiles.map(fileName => `export { default as ${fileName.replace('API', '')} } from './${fileName}';`).join('\n'));
  fs.writeFileSync(indexClassPath, codeFiles.map(fileName => `export { ${fileName} } from './${fileName}';`).join('\n'));

  // copy types
  const srcTypeDir = path.join(__dirname, `./src/type`);
  const destTypeDir = path.join(__dirname, `../api-client/type`);
  fse.copySync(srcTypeDir, destTypeDir, { overwrite: true },  function (err) {
    if (err) console.log(err);
  })
};

const generateSpecificHandleCode = ({ obj, name, ts }) => {
  if (name.toLowerCase() === 'login') {
    return `\n` + TAB + TAB + `if (res.data.data && res.data.data.token) this.request.setToken(res.data.data.token)`
  }
  if (name.toLowerCase() === 'verifytoken') {
    return `\n` + TAB + TAB + `if (res.data.data && res.data.data.token) this.request.setToken(res.data.data.token)`
  }
  if (name.toLowerCase() === 'verify2fa') {
    return `\n` + TAB + TAB + `if (res.data.data && res.data.data.token) this.request.setToken(res.data.data.token)`
  }
  if (name.toLowerCase() === 'logout') {
    return `\n` + TAB + TAB + `this.request.setToken('')`
  }
  if (name.toLowerCase() === 'contact') {
    return `\n` + TAB + TAB + `if (res.data.data && res.data.data.token) this.request.setLearnerToken(res.data.data.token)`
  }
  return '';
} 
