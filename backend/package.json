{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "pm2 start pm2-us.config.js && pm2 log", "status": "pm2 status", "log": "pm2 log", "dev": "nodemon", "jest": "NODE_PATH=./src npx jest", "test": "NODE_PATH=./src npx jest", "format": "prettier -w src/", "run-local-db": "cd ../ && docker-compose up api_db"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@fastify/formbody": "^6.0.1", "@fastify/multipart": "6.0.0", "@fastify/static": "5.0.2", "@types/jest": "^29.5.2", "@types/lodash": "^4.14.202", "adm-zip": "^0.5.10", "aws-sdk": "^2.1039.0", "axios": "^0.23.0", "bcrypt": "^5.0.1", "cron": "^1.8.2", "cross-fetch": "^3.1.5", "dotenv": "^10.0.0", "es6-promise": "^4.2.8", "exceljs": "^4.3.0", "fastify": "^3.21.6", "fastify-cors": "^6.0.2", "fastify-file-upload": "^4.0.0", "fastify-multipart": "^5.4.0", "firebase-admin": "^10.0.0", "fs-extra": "^10.1.0", "isomorphic-fetch": "^3.0.0", "joi": "^17.6.0", "joi-to-typescript": "^4.0.5", "js-events-listener": "^1.1.6", "js-htmlencode": "^0.3.0", "jwt-simple": "^0.5.6", "lodash": "^4.17.21", "mime": "^3.0.0", "node-localstorage": "^2.2.1", "nodemailer": "^6.7.1", "otplib": "^12.0.1", "parse-multipart-data": "^1.3.0", "pg": "^8.7.1", "pg-hstore": "^2.3.4", "postgres-array": "^3.0.2", "qrcode": "^1.5.3", "qs": "^6.10.1", "sequelize": "^6.9.0", "sharp": "^0.32.1", "unique-names-generator": "^4.7.1"}, "devDependencies": {"@types/node": "^16.10.2", "husky": "^7.0.2", "increase-version": "^1.0.4", "jest": "^28.1.3", "nodemon": "^2.0.13", "prettier": "^2.4.1", "tree-kill": "^1.2.2", "ts-jest": "^28.0.8", "ts-node": "^10.2.1", "typescript": "^4.4.3"}, "husky": {"hooks": {"pre-commit": "if git status -s | grep \"backend\"; then echo \"INCREASING VERSION\" && node ./scripts/increase-version.js && git add . ; fi"}}}