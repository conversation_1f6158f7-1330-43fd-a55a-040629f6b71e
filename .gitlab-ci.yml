stages:
  - test
  - build
  - deploy
  - update-instructions

test-backend:
  stage: test
  # image: node:14
  # services:
    # - postgres
  tags:
    - shell
  variables:
    POSTGRES_PASSWORD : 6ACDG25bb5CDE1DaG1D255FEadEadFAb
    POSTGRES_DB : pathfinder
    POSTGRES_USER : postgres
    POSTGRES_HOST : monorail.proxy.rlwy.net
    POSTGRES_PORT : 51678
    DB_CONNECTION_STRING_DEV : postgresql://postgres:<EMAIL>:51678/pathfinder
    POSTGRES_HOST_AUTH_METHOD: trust
    PROJECT_ID: pathfinder-b5405
    PRIVATE_KEY_ID: ea03f6cdc4e79a55d13299d006c4d0ec2e15840e
***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    CLIENT_EMAIL: <EMAIL>
    CLIENT_ID: 104137171085824151278
    AUTH_URI: https://accounts.google.com/o/oauth2/auth
    TOKEN_URI: https://oauth2.googleapis.com/token
    AUTH_PROVIDER_X509_CERT_URL: https://www.googleapis.com/oauth2/v1/certs
    CLIENT_X509_CERT_URL: https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-q3llm%40pathfinder-b5405.iam.gserviceaccount.com
    UNIVERSE_DOMAIN: googleapis.com

  script:
    - nvm install 14 && nvm use 14
    - sudo -u postgres psql -c "create database pathfinder;" || true
    - npm install -g yarn
    - cd backend
    - yarn install
    - yarn test --detectOpenHandles --forceExit

test-frontend:
  stage: test
  image: cypress/base:14.21.1
  services:
    - postgres
  tags:
    - cypress
  variables:
    POSTGRES_PASSWORD: pathfinder23123m12jk3bn1
    POSTGRES_DB: pathfinder
    POSTGRES_USER: pathfinder
    POSTGRES_HOST: postgres
    POSTGRES_PORT: 5432
    DB_CONNECTION_STRING_DEV: **************************************************************/pathfinder
    POSTGRES_HOST_AUTH_METHOD: trust
    DEV: 1
    SEED_ADMIN: <EMAIL>
    SEED_PASSWORD: CD%2023
    PORT: 3344
  artifacts:
    when: always
    paths:
      - cms/web/video-instructions/**/*.mp4
      - cms/web/screenshots-test/**/*.png
      - frontend/web/video-instructions/**/*.mp4
      - frontend/web/screenshots-test/**/*.png
    expire_in: 1 day
  script:
    - npm i -g serve pm2
    - cd backend
    - yarn
    - pm2 start pm2-test.config.js
    - cd ../cms
    - yarn
    - yarn build
    - serve build -l 19006 -c serve.json &
    - npx cypress run
    - cd ../frontend
    - yarn
    - yarn build
    - serve build -l 19007 -c serve.json &
    - npx cypress run

deploy-dev:
  stage: deploy
  tags:
    - shell
  only:
    - dev
  before_script:
    - "which ssh-agent || ( sudo apt-get install -qq openssh-client )"
    - eval $(ssh-agent -s)
    # cat Azure.pem | base64
    - ssh-add <(echo "$SSH_PRIVATE_KEY_DEV" | base64 -d)
    - mkdir -p ~/.ssh
    - '[[ -f /.dockerenv ]] && echo -e "Host *\n\tStrictHostKeyChecking no\n\n" > ~/.ssh/config'
  script:
    - ssh -o StrictHostKeyChecking=no <EMAIL> "source ~/.bashrc && cd /home/<USER>/pathfinder-2023 && sudo git checkout . && sudo git pull && cd backend && sudo yarn && sudo pm2 restart backend && cd ../cms && sudo yarn && sudo yarn nginx-deploy && cd ../cms && sudo yarn && sudo yarn nginx-deploy && cd ../frontend && sudo yarn && sudo yarn nginx-deploy && exit"

deploy-main-server:
  stage: deploy
  # image: node:18
  tags:
    - shell
  only:
    - main
  before_script:
    # - apt-get update -qq
    # - apt-get install -qq git
    # Setup SSH deploy keys
    - "which ssh-agent || ( sudo apt-get install -qq openssh-client )"
    - eval $(ssh-agent -s)
    # cat Azure.pem | base64
    - ssh-add <(echo "$LIVE_US_SV_KEY" | base64 -d)
    - mkdir -p ~/.ssh
    - '[[ -f /.dockerenv ]] && echo -e "Host *\n\tStrictHostKeyChecking no\n\n" > ~/.ssh/config'
  script:
    - ssh -o StrictHostKeyChecking=no <EMAIL> "cd /home/<USER>/pathfinder-2023 && sudo git checkout . && sudo git pull && cd backend && sudo yarn && sudo pm2 restart backend && exit"

deploy-sub-server:
  stage: deploy
  # image: node:18
  tags:
    - shell
  only:
    - main
  before_script:
    # - apt-get update -qq
    # - apt-get install -qq git
    # Setup SSH deploy keys
    - "which ssh-agent || ( sudo apt-get install -qq openssh-client )"
    - eval $(ssh-agent -s)
    # cat Azure.pem | base64
    - ssh-add <(echo "$LIVE_UK_SV_KEY" | base64 -d)
    - mkdir -p ~/.ssh
    - '[[ -f /.dockerenv ]] && echo -e "Host *\n\tStrictHostKeyChecking no\n\n" > ~/.ssh/config'
  script:
    - ssh -o StrictHostKeyChecking=no <EMAIL> "cd /home/<USER>/pathfinder-2023 && sudo git checkout . && sudo git pull && cd backend && sudo yarn && sudo pm2 restart backend && exit"

# upload-screen-recordings-to-s3:
#   stage: update-instructions
#   image: python:3.6
#   only:
#     - main
#   variables:
#     BUCKET_NAME: "pathfinder-rebuild"
#   script:
#     - pip install awscli
#     - aws s3 sync ./cms/web/video-instructions s3://$BUCKET_NAME/video-instructions --acl bucket-owner-full-control --acl public-read
#     - aws s3 sync ./frontend/web/video-instructions s3://$BUCKET_NAME/video-instructions-frontend --acl bucket-owner-full-control --acl public-read

build cms:
  stage: build
  # image: node:14
  tags:
    - shell
  script:
    - nvm install 14 && nvm use 14
    - npm install -g yarn
    - cd cms
    - yarn
    - CI=false yarn build
  only:
    - main
  artifacts:
    expire_in: 1 day
    paths:
    - cms/build/

deploy cms to s3 live:
  stage: deploy
  # image: python:3.6
  environment:
    name: admin.harborpathfinder.com
  tags:
    - shell
  only:
    - main
  variables:
    BUCKET_NAME: "admin.harborpathfinder.com"
    CLOUD_FRONT_DISTRIBUTION_ID: "E158P5K4EOHS5L"
  script:
    # - pip install awscli
    - aws s3 sync ./cms/build s3://$BUCKET_NAME/ --acl bucket-owner-full-control --acl public-read
    - aws cloudfront create-invalidation --distribution-id $CLOUD_FRONT_DISTRIBUTION_ID --paths "/*"

build frontend:
  stage: build
  # image: node:14
  tags:
    - shell
  script:
    - nvm install 14 && nvm use 14
    - npm install -g yarn
    - cd frontend
    - yarn
    - CI=false yarn build
  only:
    - main
  artifacts:
    expire_in: 1 day
    paths:
    - frontend/build/

deploy frontend to s3 live:
  stage: deploy
  # image: python:3.6
  tags:
    - shell
  environment:
    name: learner.harborpathfinder.com
  only:
    - main
  variables:
    BUCKET_NAME: "learner.harborpathfinder.com"
    CLOUD_FRONT_DISTRIBUTION_ID: "E1FTOD06W05KA1"
  script:
    # - pip install awscli
    - aws s3 sync ./frontend/build s3://$BUCKET_NAME/ --acl bucket-owner-full-control --acl public-read
    - aws cloudfront create-invalidation --distribution-id $CLOUD_FRONT_DISTRIBUTION_ID --paths "/*"
