

export type TOrder = {
    id: string;
    resellerId: string;
    productId: string;
    price: number;
    amount: number;
    variationName: string;
    status: string;
    downloadIds: {
        [downloadId: string]: number;
    };
    didUseAllDownload: boolean;
}

export type TDownload = {
    id: string; // printJobId
    queueId?: number;
    resellerId: string;
    productId: string;
    designId: string;
    linkedOrderId: string;
    variationName: string;
    pdf: string;
}