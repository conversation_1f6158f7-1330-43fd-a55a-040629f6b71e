import { TL<PERSON><PERSON> } from "./TypeLearner";
import { TUser } from "./TypeUser";

export interface TypeAPIHandler {
  url: string;
  method: string;
  middlewares?: Array<any>;
  handler(req: any, res: any): void;
  [additionMethods: string]: any;
}

export interface TRequestUser {
  user?: TUser;
  [anyOther: string]: any;
}

export interface TRequestLearner {
  learner?: TLearner;
  [anyOther: string]: any;
}
